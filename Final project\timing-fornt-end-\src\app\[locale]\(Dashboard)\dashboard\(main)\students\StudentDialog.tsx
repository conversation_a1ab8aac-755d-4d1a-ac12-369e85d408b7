"use client";

import Button from "@/lib/ui/components/global/Buttons/Button";
import { Plus } from "lucide-react";
import { useState } from "react";
import Dialog from "@/lib/ui/components/global/Dialog/Dialog";
import CreateStudentForm from "@/lib/ui/forms/student/CreateStudentForm";

export function StudentDialog() {
    const [isDialogOpen, setIsDialogOpen] = useState(false);

    const handleOpenDialog = () => {
        setIsDialogOpen(true);
    };

    const handleCloseDialog = () => {
        setIsDialogOpen(false);
    };

    return (
        <>
            <div className="flex justify-between items-center">
                <Button
                    mode="filled"
                    onClick={handleOpenDialog}
                    icon={<Plus />}
                >
                    Add Student
                </Button>
            </div>
            <Dialog
                isOpen={isDialogOpen}
                onClose={handleCloseDialog}
                title="Create New Student"
            >
                <CreateStudentForm onSuccess={handleCloseDialog} />
            </Dialog>
        </>
    );
} 