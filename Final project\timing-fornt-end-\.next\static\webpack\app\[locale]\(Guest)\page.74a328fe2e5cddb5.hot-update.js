"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(Guest)/page",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6fca4e2bf5c4\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHByb1xcRGVza3RvcFxcZmluIHByb2ogY29waWUgMlxcRmluYWwgcHJvamVjdFxcdGltaW5nLWZvcm50LWVuZC1cXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjZmY2E0ZTJiZjVjNFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/ui/forms/TimingForms/TeacherValidClasses.tsx":
/*!**************************************************************!*\
  !*** ./src/lib/ui/forms/TimingForms/TeacherValidClasses.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TeacherValidClasses)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Loader2_Plus_Send_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Loader2,Plus,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Loader2_Plus_Send_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Loader2,Plus,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Loader2_Plus_Send_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Loader2,Plus,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Loader2_Plus_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Loader2,Plus,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _components_global_Buttons_Button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../components/global/Buttons/Button */ \"(app-pages-browser)/./src/lib/ui/components/global/Buttons/Button.tsx\");\n/* harmony import */ var _components_global_Modal_Modal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/global/Modal/Modal */ \"(app-pages-browser)/./src/lib/ui/components/global/Modal/Modal.tsx\");\n/* harmony import */ var _components_global_Inputs_inputs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/global/Inputs/inputs */ \"(app-pages-browser)/./src/lib/ui/components/global/Inputs/inputs.tsx\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _components_global_Inputs_SimpleSelect__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/global/Inputs/SimpleSelect */ \"(app-pages-browser)/./src/lib/ui/components/global/Inputs/SimpleSelect.tsx\");\n/* harmony import */ var _lib_server_actions_sectionTiming_SectionTimingActions__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/server/actions/sectionTiming/SectionTimingActions */ \"(app-pages-browser)/./src/lib/server/actions/sectionTiming/SectionTimingActions.ts\");\n/* harmony import */ var _lib_server_actions_request_requestActions__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/server/actions/request/requestActions */ \"(app-pages-browser)/./src/lib/server/actions/request/requestActions.ts\");\n/* harmony import */ var _lib_hooks_useCurrentUser__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/hooks/useCurrentUser */ \"(app-pages-browser)/./src/lib/hooks/useCurrentUser.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst FormSchema = zod__WEBPACK_IMPORTED_MODULE_10__.z.object({\n    \"start_time\": zod__WEBPACK_IMPORTED_MODULE_10__.z.string(),\n    \"end_time\": zod__WEBPACK_IMPORTED_MODULE_10__.z.string(),\n    \"day\": zod__WEBPACK_IMPORTED_MODULE_10__.z.string({\n        required_error: \"Day is required\"\n    }).refine((val)=>[\n            \"mon\",\n            \"tue\",\n            \"wed\",\n            \"thu\",\n            \"fri\",\n            \"sat\",\n            \"sun\"\n        ].includes(val), {\n        message: \"Invalid day\"\n    }),\n    \"type\": zod__WEBPACK_IMPORTED_MODULE_10__.z.string({\n        required_error: \"Type is required\"\n    }).refine((val)=>[\n            \"td\",\n            \"tp\",\n            \"course\"\n        ].includes(val), {\n        message: \"Invalid type\"\n    }).optional(),\n    \"class_rome_id\": zod__WEBPACK_IMPORTED_MODULE_10__.z.coerce.number().optional(),\n    \"day_id\": zod__WEBPACK_IMPORTED_MODULE_10__.z.coerce.number().optional(),\n    \"module_id\": zod__WEBPACK_IMPORTED_MODULE_10__.z.coerce.number().optional(),\n    \"teacher_id\": zod__WEBPACK_IMPORTED_MODULE_10__.z.coerce.number().optional(),\n    \"group_id\": zod__WEBPACK_IMPORTED_MODULE_10__.z.coerce.number().optional(),\n    \"message\": zod__WEBPACK_IMPORTED_MODULE_10__.z.string().optional()\n});\nfunction TeacherValidClasses() {\n    var _errors_start_time, _errors_end_time, _errors_day, _errors_class_rome_id, _errors_start_time1, _errors_end_time1, _errors_day_id, _errors_group_id, _errors_module_id, _user_key_keyable, _user_key, _user_key_keyable1, _user_key1, _errors_type, _errors_message;\n    _s();\n    const { user, loading: userLoading, error: userError } = (0,_lib_hooks_useCurrentUser__WEBPACK_IMPORTED_MODULE_8__.useCurrentUser)();\n    const [groups, setGroups] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)([\n        {\n            id: 1,\n            number: 1\n        },\n        {\n            id: 2,\n            number: 2\n        },\n        {\n            id: 3,\n            number: 3\n        },\n        {\n            id: 4,\n            number: 4\n        },\n        {\n            id: 5,\n            number: 5\n        },\n        {\n            id: 6,\n            number: 6\n        },\n        {\n            id: 7,\n            number: 7\n        }\n    ]);\n    const [sectionId] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(1);\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)([]);\n    const [days, setDays] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)();\n    const [requestSuccess, setRequestSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    const [requestError, setRequestError] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)({\n        \"TeacherValidClasses.useEffect\": ()=>{\n            const fetchData = {\n                \"TeacherValidClasses.useEffect.fetchData\": async ()=>{\n                    try {\n                        const [daysData] = await Promise.all([\n                            (0,_lib_server_actions_sectionTiming_SectionTimingActions__WEBPACK_IMPORTED_MODULE_6__.getDays)(1)\n                        ]);\n                        setDays(daysData);\n                    } catch (error) {\n                        console.error(\"Error fetching data:\", error);\n                    }\n                }\n            }[\"TeacherValidClasses.useEffect.fetchData\"];\n            fetchData();\n        }\n    }[\"TeacherValidClasses.useEffect\"], []);\n    const { register, handleSubmit, formState: { errors, isSubmitting, isSubmitSuccessful } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_11__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__.zodResolver)(FormSchema)\n    });\n    console.log(errors);\n    const onSubmit = async (data)=>{\n        setRequestSuccess(false);\n        setRequestError(null);\n        if ((classes === null || classes === void 0 ? void 0 : classes.length) === 0) {\n            // First step: Search for valid classes\n            const data_payload = {\n                start_time: data.start_time,\n                end_time: data.end_time,\n                day: data.day\n            };\n            try {\n                const response = await (0,_lib_server_actions_sectionTiming_SectionTimingActions__WEBPACK_IMPORTED_MODULE_6__.validClassRoom)(sectionId, data_payload);\n                setClasses(response);\n            } catch (error) {\n                console.error('Error fetching classes:', error);\n                setRequestError('Failed to find valid classes');\n            }\n        } else {\n            // Second step: Submit request to admin\n            if (!data.class_rome_id || !data.module_id || !data.type) {\n                setRequestError('Please fill in all required fields');\n                return;\n            }\n            if (!user || user.key.keyable_type !== 'teacher') {\n                setRequestError('You must be logged in as a teacher to submit requests');\n                return;\n            }\n            try {\n                const requestData = {\n                    teacher_id: user.key.keyable.id,\n                    section_id: sectionId,\n                    group_id: data.group_id,\n                    module_id: data.module_id,\n                    class_rome_id: data.class_rome_id,\n                    day: data.day,\n                    start_time: data.start_time,\n                    end_time: data.end_time,\n                    type: data.type,\n                    message: data.message\n                };\n                const result = await (0,_lib_server_actions_request_requestActions__WEBPACK_IMPORTED_MODULE_7__.createRequest)(requestData);\n                if (result.success) {\n                    setRequestSuccess(true);\n                    setClasses([]); // Reset form\n                } else {\n                    setRequestError(result.message);\n                }\n            } catch (error) {\n                console.error('Error submitting request:', error);\n                setRequestError('Failed to submit request');\n            }\n        }\n    };\n    if (userLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"my-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Buttons_Button__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Loader2_Plus_Send_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"animate-spin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 31\n                }, void 0),\n                mode: \"filled\",\n                disabled: true,\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                lineNumber: 158,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n            lineNumber: 157,\n            columnNumber: 13\n        }, this);\n    }\n    if (userError || !user || user.key.keyable_type !== 'teacher') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"my-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-red-500\",\n                children: \"You must be logged in as a teacher to access this feature.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                lineNumber: 168,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n            lineNumber: 167,\n            columnNumber: 13\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"my-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Buttons_Button__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Loader2_Plus_Send_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 27\n                }, void 0),\n                mode: \"filled\",\n                onClick: ()=>(0,_components_global_Modal_Modal__WEBPACK_IMPORTED_MODULE_2__.openModal)(\"lessen-timing-form\"),\n                children: \"Search Valid Class\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                lineNumber: 175,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Modal_Modal__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                id: \"lessen-timing-form\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-1/3 flex flex-col gap-4 items-center justify-center p-4 bg-surface-container dark:bg-dark-surface-container rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-title-large font-bold text-on-surface dark:text-dark-on-surface\",\n                            children: \"Find A Valid Class\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 21\n                        }, this),\n                        requestSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-2 items-center justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Loader2_Plus_Send_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    size: 24,\n                                    className: \"text-green-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-green-500\",\n                                    children: \"Request sent successfully to admin!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 25\n                        }, this),\n                        requestError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-2 items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-500\",\n                                children: requestError\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 25\n                        }, this),\n                        (classes === null || classes === void 0 ? void 0 : classes.length) > 0 && !requestSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-2 items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-500\",\n                                children: [\n                                    \"Found \",\n                                    classes.length,\n                                    \" available classrooms. Please complete the form to send your request.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit(onSubmit),\n                            className: \"flex flex-col gap-4 items-center justify-center\",\n                            children: [\n                                (classes === null || classes === void 0 ? void 0 : classes.length) === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Inputs_inputs__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            error: (_errors_start_time = errors.start_time) === null || _errors_start_time === void 0 ? void 0 : _errors_start_time.message,\n                                            register: register,\n                                            label: \"start_time\",\n                                            title: \"Start Time\",\n                                            placeholder: \"Start Time (HH:MM:SS)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Inputs_inputs__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            error: (_errors_end_time = errors.end_time) === null || _errors_end_time === void 0 ? void 0 : _errors_end_time.message,\n                                            register: register,\n                                            label: \"end_time\",\n                                            title: \"End Time\",\n                                            placeholder: \"End Time (HH:MM:SS)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Inputs_SimpleSelect__WEBPACK_IMPORTED_MODULE_5__.SimpleSelect, {\n                                            register: register(\"day\"),\n                                            error: (_errors_day = errors.day) === null || _errors_day === void 0 ? void 0 : _errors_day.message,\n                                            label: \"day\",\n                                            title: \"Day\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select a day\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"mon\",\n                                                    children: \"Monday\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"tue\",\n                                                    children: \"Tuesday\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"wed\",\n                                                    children: \"Wednesday\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"thu\",\n                                                    children: \"Thursday\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"fri\",\n                                                    children: \"Friday\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"sat\",\n                                                    children: \"Saturday\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"sun\",\n                                                    children: \"Sunday\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 41\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 37\n                                        }, this)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Inputs_SimpleSelect__WEBPACK_IMPORTED_MODULE_5__.SimpleSelect, {\n                                            register: register(\"class_rome_id\"),\n                                            error: (_errors_class_rome_id = errors.class_rome_id) === null || _errors_class_rome_id === void 0 ? void 0 : _errors_class_rome_id.message,\n                                            label: \"class_rome_id\",\n                                            title: \"Class\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select a class\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 41\n                                                }, this),\n                                                classes === null || classes === void 0 ? void 0 : classes.map((class_)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: class_.id,\n                                                        children: class_.number\n                                                    }, class_.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 45\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Inputs_inputs__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            error: (_errors_start_time1 = errors.start_time) === null || _errors_start_time1 === void 0 ? void 0 : _errors_start_time1.message,\n                                            register: register,\n                                            label: \"start_time\",\n                                            title: \"Start Time\",\n                                            placeholder: \"Start Time (HH:MM:SS)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Inputs_inputs__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            error: (_errors_end_time1 = errors.end_time) === null || _errors_end_time1 === void 0 ? void 0 : _errors_end_time1.message,\n                                            register: register,\n                                            label: \"end_time\",\n                                            title: \"End Time\",\n                                            placeholder: \"End Time (HH:MM:SS)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Inputs_SimpleSelect__WEBPACK_IMPORTED_MODULE_5__.SimpleSelect, {\n                                            register: register(\"day_id\"),\n                                            error: (_errors_day_id = errors.day_id) === null || _errors_day_id === void 0 ? void 0 : _errors_day_id.message,\n                                            label: \"day_id\",\n                                            title: \"Day\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select a day\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 41\n                                                }, this),\n                                                days === null || days === void 0 ? void 0 : days.map((day)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: day.id,\n                                                        children: day.name\n                                                    }, day.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 45\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Inputs_SimpleSelect__WEBPACK_IMPORTED_MODULE_5__.SimpleSelect, {\n                                            register: register(\"group_id\"),\n                                            error: (_errors_group_id = errors.group_id) === null || _errors_group_id === void 0 ? void 0 : _errors_group_id.message,\n                                            label: \"group_id\",\n                                            title: \"Group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select a group\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 41\n                                                }, this),\n                                                groups === null || groups === void 0 ? void 0 : groups.map((group)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: group.id,\n                                                        children: group.number\n                                                    }, group.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 45\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 38\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Inputs_SimpleSelect__WEBPACK_IMPORTED_MODULE_5__.SimpleSelect, {\n                                            register: register(\"module_id\"),\n                                            error: (_errors_module_id = errors.module_id) === null || _errors_module_id === void 0 ? void 0 : _errors_module_id.message,\n                                            label: \"module_id\",\n                                            title: \"Module\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select a module\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"1\",\n                                                    children: \"Mathematics\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"2\",\n                                                    children: \"Physics\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"3\",\n                                                    children: \"Chemistry\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 41\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: \"Teacher:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 41\n                                                }, this),\n                                                \" \",\n                                                user === null || user === void 0 ? void 0 : (_user_key = user.key) === null || _user_key === void 0 ? void 0 : (_user_key_keyable = _user_key.keyable) === null || _user_key_keyable === void 0 ? void 0 : _user_key_keyable.name,\n                                                \" \",\n                                                user === null || user === void 0 ? void 0 : (_user_key1 = user.key) === null || _user_key1 === void 0 ? void 0 : (_user_key_keyable1 = _user_key1.keyable) === null || _user_key_keyable1 === void 0 ? void 0 : _user_key_keyable1.last\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Inputs_SimpleSelect__WEBPACK_IMPORTED_MODULE_5__.SimpleSelect, {\n                                            register: register(\"type\"),\n                                            error: (_errors_type = errors.type) === null || _errors_type === void 0 ? void 0 : _errors_type.message,\n                                            label: \"type\",\n                                            title: \"Class Type\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select type\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"course\",\n                                                    children: \"Course\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"td\",\n                                                    children: \"TD\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"tp\",\n                                                    children: \"TP\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 41\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Inputs_inputs__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            error: (_errors_message = errors.message) === null || _errors_message === void 0 ? void 0 : _errors_message.message,\n                                            register: register,\n                                            label: \"message\",\n                                            title: \"Message (Optional)\",\n                                            placeholder: \"Additional message for admin...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 37\n                                        }, this)\n                                    ]\n                                }, void 0, true),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full flex justify-end\",\n                                    children: (classes === null || classes === void 0 ? void 0 : classes.length) === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Buttons_Button__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        type: \"submit\",\n                                        icon: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Loader2_Plus_Send_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 80\n                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Loader2_Plus_Send_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 119\n                                        }, void 0),\n                                        mode: \"filled\",\n                                        children: \"Find Classes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 37\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Buttons_Button__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        type: \"submit\",\n                                        icon: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Loader2_Plus_Send_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 80\n                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Loader2_Plus_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 119\n                                        }, void 0),\n                                        mode: \"filled\",\n                                        children: \"Send Request\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 37\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                lineNumber: 178,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n        lineNumber: 174,\n        columnNumber: 9\n    }, this);\n}\n_s(TeacherValidClasses, \"paaNTivynVpUQtFi9YLHLteznbM=\", false, function() {\n    return [\n        _lib_hooks_useCurrentUser__WEBPACK_IMPORTED_MODULE_8__.useCurrentUser,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_11__.useForm\n    ];\n});\n_c = TeacherValidClasses;\nvar _c;\n$RefreshReg$(_c, \"TeacherValidClasses\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/ui/forms/TimingForms/TeacherValidClasses.tsx\n"));

/***/ })

});