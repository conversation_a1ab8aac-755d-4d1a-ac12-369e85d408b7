"use client";
import { <PERSON>, <PERSON>, <PERSON>, <PERSON> } from "lucide-react"
import Modal, { openModal } from "../../global/Modal/Modal"
import { useEffect, useState } from "react"
import { getRequests, updateRequestStatus, getPendingRequestsCount } from "@/lib/server/actions/request/requestActions"
import type { RequestResponse } from "@/lib/server/actions/request/requestActions"
import Button from "../../global/Buttons/Button"

export default function Request() {
    const [requests, setRequests] = useState<RequestResponse[]>([])
    const [pendingCount, setPendingCount] = useState(0)
    const [loading, setLoading] = useState(false)

    const fetchRequests = async () => {
        try {
            setLoading(true)
            const [requestsData, countData] = await Promise.all([
                getRequests(1, 'pending'),
                getPendingRequestsCount()
            ])
            setRequests(requestsData.data)
            setPendingCount(countData)
        } catch (error) {
            console.error('Error fetching requests:', error)
        } finally {
            setLoading(false)
        }
    }

    useEffect(() => {
        fetchRequests()
    }, [])

    const handleRequestAction = async (requestId: number, status: 'approved' | 'rejected') => {
        try {
            await updateRequestStatus(requestId, status)
            await fetchRequests() // Refresh the list
        } catch (error) {
            console.error('Error updating request:', error)
        }
    }

    return (
        <>
            <div className="relative">
                <Bell
                    className="text-primary dark:text-dark-primary cursor-pointer"
                    size={24}
                    onClick={() => openModal("request-modal")}
                />
                {pendingCount > 0 && (
                    <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                        {pendingCount}
                    </span>
                )}
            </div>
            <Modal id="request-modal">
                <div className="flex flex-col gap-4 h-[60vh] w-2/3 max-w-4xl">
                    <h2 className="text-xl font-bold text-on-surface dark:text-dark-on-surface">
                        Teacher Requests ({pendingCount} pending)
                    </h2>

                    {loading ? (
                        <div className="flex items-center justify-center h-32">
                            <Clock className="animate-spin" size={24} />
                            <span className="ml-2">Loading requests...</span>
                        </div>
                    ) : requests.length === 0 ? (
                        <div className="flex items-center justify-center h-32 text-gray-500">
                            No pending requests
                        </div>
                    ) : (
                        <ul className="flex flex-col gap-4 overflow-y-auto p-4 border rounded-lg">
                            {requests.map((request) => (
                                <li key={request.id} className="flex flex-col gap-2 p-4 bg-surface-container dark:bg-dark-surface-container rounded-lg shadow">
                                    <div className="flex justify-between items-start">
                                        <div className="flex flex-col gap-1">
                                            <h3 className="font-semibold text-lg">
                                                {request.teacher.name} {request.teacher.last}
                                            </h3>
                                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                                @{request.teacher.username}
                                            </p>
                                        </div>
                                        <div className="text-right">
                                            <span className="text-sm text-primary-container dark:text-dark-primary-container">
                                                {request.start_time} - {request.end_time}
                                            </span>
                                            <p className="text-xs text-gray-500">
                                                {request.type.toUpperCase()} • {request.day}
                                            </p>
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-2 gap-4 text-sm">
                                        <div>
                                            <span className="font-medium">Module:</span> {request.module.name}
                                        </div>
                                        <div>
                                            <span className="font-medium">Classroom:</span> {request.classRome.name}
                                        </div>
                                        {request.section && (
                                            <div>
                                                <span className="font-medium">Section:</span> {request.section.name}
                                            </div>
                                        )}
                                        {request.group && (
                                            <div>
                                                <span className="font-medium">Group:</span> {request.group.number}
                                            </div>
                                        )}
                                    </div>

                                    {request.message && (
                                        <div className="mt-2 p-2 bg-gray-50 dark:bg-gray-800 rounded">
                                            <span className="font-medium">Message:</span> {request.message}
                                        </div>
                                    )}

                                    <div className="flex gap-2 mt-3">
                                        <Button
                                            mode="filled"
                                            icon={<Check size={16} />}
                                            onClick={() => handleRequestAction(request.id, 'approved')}
                                            className="bg-green-500 hover:bg-green-600"
                                        >
                                            Approve
                                        </Button>
                                        <Button
                                            mode="outlined"
                                            icon={<X size={16} />}
                                            onClick={() => handleRequestAction(request.id, 'rejected')}
                                            className="border-red-500 text-red-500 hover:bg-red-50"
                                        >
                                            Reject
                                        </Button>
                                    </div>
                                </li>
                            ))}
                        </ul>
                    )}
                </div>
            </Modal>
        </>
    )
}