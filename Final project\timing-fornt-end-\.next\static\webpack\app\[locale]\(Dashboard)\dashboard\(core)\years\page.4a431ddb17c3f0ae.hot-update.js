"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(Dashboard)/dashboard/(core)/years/page",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"92a087c46b07\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHByb1xcRGVza3RvcFxcZmluIHByb2ogY29waWUgMlxcRmluYWwgcHJvamVjdFxcdGltaW5nLWZvcm50LWVuZC1cXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjkyYTA4N2M0NmIwN1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/server/actions/year/yearActions.ts":
/*!****************************************************!*\
  !*** ./src/lib/server/actions/year/yearActions.ts ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createYear: () => (/* binding */ createYear),\n/* harmony export */   deleteYear: () => (/* binding */ deleteYear),\n/* harmony export */   getYears: () => (/* binding */ getYears),\n/* harmony export */   updateYear: () => (/* binding */ updateYear)\n/* harmony export */ });\n/* harmony import */ var private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! private-next-rsc-action-client-wrapper */ \"(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js\");\n/* harmony import */ var private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_action_entry_do_not_use__ {\"005a719cd5520d15a48a60ff42bd5ff777aeb832af\":\"getYears\",\"408f077575bbe410982b974e5423fe22a30176f7f3\":\"deleteYear\",\"40cfbda5234f8c2e851115a7761ef8f118173342ef\":\"createYear\",\"6040436350418a09795ac12e7860e063d2fadf926c\":\"updateYear\"} */ \nvar getYears = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"005a719cd5520d15a48a60ff42bd5ff777aeb832af\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getYears\");\nvar createYear = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"40cfbda5234f8c2e851115a7761ef8f118173342ef\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"createYear\");\nvar updateYear = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"6040436350418a09795ac12e7860e063d2fadf926c\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"updateYear\");\nvar deleteYear = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"408f077575bbe410982b974e5423fe22a30176f7f3\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"deleteYear\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/server/actions/year/yearActions.ts\n"));

/***/ })

});