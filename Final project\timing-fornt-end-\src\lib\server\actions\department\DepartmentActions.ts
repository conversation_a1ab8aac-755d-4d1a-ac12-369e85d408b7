'use server'

import axiosInstance from '@/lib/server/tools/axios'
import { DepartmentResponse, Department } from '../../types/departments/allDepartments'
import { revalidatePath } from 'next/cache'

export async function getAllDepartments(): Promise<DepartmentResponse> {
    try {
        const { data } = await axiosInstance.get<DepartmentResponse>(
            `/allDepartments`,
        )
        return data
    } catch (error: any) {
        console.error('Error fetching departments:', error.response?.data)
        throw error
    }
}

export async function getDepartments(): Promise<{ departments: Department[] }> {
    try {
        const { data } = await axiosInstance.get<{ data: Department[] }>(
            `/departments`,
        )
        return { departments: data.data }
    } catch (error: any) {
        console.error('Error fetching departments:', error.response?.data)
        throw error
    }
}

export interface CreateDepartmentRequest {
    name: string;
}

export interface DepartmentErrorResponse {
    message: string;
}

export async function createDepartment(departmentData: CreateDepartmentRequest): Promise<Department | DepartmentErrorResponse> {
    try {
        const { data } = await axiosInstance.post<Department>(
            `/departments`,
            departmentData
        )
        revalidatePath('/dashboard/departements')
        revalidatePath('/dashboard')
        return data
    } catch (error: any) {
        console.error('Error creating department:', error.response?.data)
        if (error.response?.data) {
            return error.response.data as DepartmentErrorResponse
        }
        throw error
    }
}
