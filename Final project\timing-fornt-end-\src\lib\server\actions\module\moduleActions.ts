'use server'

import { Mo<PERSON><PERSON>, CreateModuleRequest, ModuleErrorResponse } from '@/lib/server/types/module/module'
import axiosInstance from '@/lib/server/tools/axios'
import { revalidatePath } from 'next/cache'

interface ModulePaginatedResponse {
    data: Module[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
}

export async function getModules(): Promise<ModulePaginatedResponse> {
    try {
        const { data } = await axiosInstance.get<ModulePaginatedResponse>(`/modules`)
        return data
    } catch (error: any) {
        console.error('Error fetching modules:', error.response?.data)
        throw error
    }
}

export async function createModule(moduleData: CreateModuleRequest): Promise<Module | ModuleErrorResponse> {
    try {
        const { data } = await axiosInstance.post<Module>(
            `/modules`,
            moduleData
        )
        // Revalidate the modules page and related paths
        revalidatePath('/dashboard/modules')
        revalidatePath('/dashboard/modules', 'page')
        revalidatePath('/dashboard')
        return data
    } catch (error: any) {
        console.error('Error creating module:', error.response?.data)
        if (error.response?.data) {
            return error.response.data as ModuleErrorResponse
        }
        throw error
    }
}

export async function updateModule(id: number, moduleData: Partial<Module>): Promise<Module | ModuleErrorResponse> {
    try {
        const { data } = await axiosInstance.put<Module>(
            `/modules/${id}`,
            moduleData
        )
        // Revalidate the modules page and related paths
        revalidatePath('/dashboard/modules')
        revalidatePath('/dashboard/modules', 'page')
        revalidatePath('/dashboard')
        return data
    } catch (error: any) {
        console.error('Error updating module:', error.response?.data)
        if (error.response?.data) {
            return error.response.data as ModuleErrorResponse
        }
        throw error
    }
}

export async function deleteModule(id: number): Promise<{ success: boolean }> {
    try {
        await axiosInstance.delete(`/modules/${id}`)
        // Revalidate the modules page and related paths
        revalidatePath('/dashboard/modules')
        revalidatePath('/dashboard/modules', 'page')
        revalidatePath('/dashboard')
        return { success: true }
    } catch (error) {
        console.error('Error deleting module:', error)
        throw error
    }
}
