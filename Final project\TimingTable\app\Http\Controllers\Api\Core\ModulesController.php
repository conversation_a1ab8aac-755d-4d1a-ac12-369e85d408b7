<?php

namespace App\Http\Controllers\Api\Core;

use App\Http\Controllers\Controller;
use App\Models\Api\Core\Module;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ModulesController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): JsonResponse
    {
        $modules = Module::with(['sessions'])
            ->paginate(10);
        
        return response()->json($modules);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:modules,name',
        ]);

        $module = Module::create([
            'name' => $request->name,
        ]);

        return response()->json($module->load(['sessions']), 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Module $module): JsonResponse
    {
        return response()->json($module->load(['sessions']));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Module $module): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:modules,name,' . $module->id,
        ]);

        $module->update([
            'name' => $request->name,
        ]);

        return response()->json($module->load(['sessions']));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Module $module): JsonResponse
    {
        $module->delete();
        
        return response()->json(['message' => 'Module deleted successfully']);
    }
}
