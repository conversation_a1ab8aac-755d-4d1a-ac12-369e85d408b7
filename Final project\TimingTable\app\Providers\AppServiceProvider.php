<?php

namespace App\Providers;

use App\Models\Api\Main\Group;
use App\Models\Api\Main\Section;
use App\Models\Api\Users\Admin;
use App\Models\Api\Users\Student;
use App\Models\Api\Users\Teacher;
use App\Models\User;
use Illuminate\Auth\Notifications\ResetPassword;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\ServiceProvider;
use Laravel\Sanctum\PersonalAccessToken;
use Laravel\Sanctum\Sanctum;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Sanctum::usePersonalAccessTokenModel(PersonalAccessToken::class);

        Relation::morphMap([
            'user' => User::class ,
            'admin' => Admin::class,
            'student' => Student::class,
            'teacher' => Teacher::class,
            'group' => Group::class,
            'section' => Section::class,
        ]);

        ResetPassword::createUrlUsing(function (object $notifiable, string $token) {
            return config('app.frontend_url')."/password-reset/$token?email={$notifiable->getEmailForPasswordReset()}";
        });
    }
}
