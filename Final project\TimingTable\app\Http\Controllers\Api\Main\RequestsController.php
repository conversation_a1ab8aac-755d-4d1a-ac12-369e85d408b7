<?php

namespace App\Http\Controllers\Api\Main;

use App\Http\Controllers\Controller;
use App\Models\Api\Main\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request as HttpRequest;
use Illuminate\Support\Facades\Auth;

class RequestsController extends Controller
{
    /**
     * Display a listing of requests for admins
     */
    public function index(HttpRequest $request): JsonResponse
    {
        $requests = Request::with([
            'teacher',
            'section',
            'group',
            'module',
            'classRome',
            'reviewedBy'
        ])
        ->when($request->has('status'), function ($query) use ($request) {
            $query->where('status', $request->status);
        })
        ->orderBy('created_at', 'desc')
        ->paginate(10);

        return response()->json($requests);
    }

    /**
     * Store a newly created request
     */
    public function store(HttpRequest $request): JsonResponse
    {
        $validated = $request->validate([
            'teacher_id' => 'required|exists:teachers,id',
            'section_id' => 'nullable|exists:sections,id',
            'group_id' => 'nullable|exists:groups,id',
            'module_id' => 'required|exists:modules,id',
            'class_rome_id' => 'required|exists:class_romes,id',
            'day' => 'required|in:mon,tue,wed,thu,fri,sat,sun',
            'start_time' => 'required|date_format:H:i:s',
            'end_time' => 'required|date_format:H:i:s|after:start_time',
            'type' => 'required|in:td,tp,course',
            'message' => 'nullable|string|max:1000',
        ]);

        $teacherRequest = Request::create($validated);

        return response()->json([
            'message' => 'Request submitted successfully',
            'request' => $teacherRequest->load([
                'teacher',
                'section',
                'group',
                'module',
                'classRome'
            ])
        ], 201);
    }

    /**
     * Display the specified request
     */
    public function show(Request $request): JsonResponse
    {
        return response()->json([
            'request' => $request->load([
                'teacher',
                'section',
                'group',
                'module',
                'classRome',
                'reviewedBy'
            ])
        ]);
    }

    /**
     * Update request status (approve/reject)
     */
    public function update(HttpRequest $httpRequest, Request $request): JsonResponse
    {
        $validated = $httpRequest->validate([
            'status' => 'required|in:approved,rejected',
            'admin_response' => 'nullable|string|max:1000',
        ]);

        $user = Auth::user();
        $admin = $user->key->keyable;

        $request->update([
            'status' => $validated['status'],
            'admin_response' => $validated['admin_response'] ?? null,
            'reviewed_by' => $admin->id,
            'reviewed_at' => now(),
        ]);

        return response()->json([
            'message' => 'Request updated successfully',
            'request' => $request->load([
                'teacher',
                'section',
                'group',
                'module',
                'classRome',
                'reviewedBy'
            ])
        ]);
    }

    /**
     * Remove the specified request
     */
    public function destroy(Request $request): JsonResponse
    {
        $request->delete();

        return response()->json([
            'message' => 'Request deleted successfully'
        ]);
    }

    /**
     * Get pending requests count for notifications
     */
    public function pendingCount(): JsonResponse
    {
        $count = Request::pending()->count();

        return response()->json([
            'count' => $count
        ]);
    }

    /**
     * Get teacher's own requests
     */
    public function teacherRequests(HttpRequest $request): JsonResponse
    {
        $user = Auth::user();
        $teacher = $user->key->keyable;

        $requests = Request::where('teacher_id', $teacher->id)
            ->with([
                'section',
                'group',
                'module',
                'classRome',
                'reviewedBy'
            ])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return response()->json($requests);
    }
}
