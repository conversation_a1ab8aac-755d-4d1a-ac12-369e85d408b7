"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(Guest)/page",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"57bea613638c\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHByb1xcRGVza3RvcFxcZmluIHByb2ogY29waWUgMlxcRmluYWwgcHJvamVjdFxcdGltaW5nLWZvcm50LWVuZC1cXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjU3YmVhNjEzNjM4Y1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/ui/forms/TimingForms/TeacherValidClasses.tsx":
/*!**************************************************************!*\
  !*** ./src/lib/ui/forms/TimingForms/TeacherValidClasses.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TeacherValidClasses)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Loader2_Plus_Send_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Loader2,Plus,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Loader2_Plus_Send_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Loader2,Plus,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Loader2_Plus_Send_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Loader2,Plus,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Loader2_Plus_Send_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Loader2,Plus,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _components_global_Buttons_Button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../components/global/Buttons/Button */ \"(app-pages-browser)/./src/lib/ui/components/global/Buttons/Button.tsx\");\n/* harmony import */ var _components_global_Modal_Modal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/global/Modal/Modal */ \"(app-pages-browser)/./src/lib/ui/components/global/Modal/Modal.tsx\");\n/* harmony import */ var _components_global_Inputs_inputs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/global/Inputs/inputs */ \"(app-pages-browser)/./src/lib/ui/components/global/Inputs/inputs.tsx\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _components_global_Inputs_SimpleSelect__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/global/Inputs/SimpleSelect */ \"(app-pages-browser)/./src/lib/ui/components/global/Inputs/SimpleSelect.tsx\");\n/* harmony import */ var _lib_server_actions_sectionTiming_SectionTimingActions__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/server/actions/sectionTiming/SectionTimingActions */ \"(app-pages-browser)/./src/lib/server/actions/sectionTiming/SectionTimingActions.ts\");\n/* harmony import */ var _lib_server_actions_request_requestActions__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/server/actions/request/requestActions */ \"(app-pages-browser)/./src/lib/server/actions/request/requestActions.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_8__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst FormSchema = zod__WEBPACK_IMPORTED_MODULE_9__.z.object({\n    \"start_time\": zod__WEBPACK_IMPORTED_MODULE_9__.z.string(),\n    \"end_time\": zod__WEBPACK_IMPORTED_MODULE_9__.z.string(),\n    \"day\": zod__WEBPACK_IMPORTED_MODULE_9__.z.string({\n        required_error: \"Day is required\"\n    }).refine((val)=>[\n            \"mon\",\n            \"tue\",\n            \"wed\",\n            \"thu\",\n            \"fri\",\n            \"sat\",\n            \"sun\"\n        ].includes(val), {\n        message: \"Invalid day\"\n    }),\n    \"type\": zod__WEBPACK_IMPORTED_MODULE_9__.z.string({\n        required_error: \"Type is required\"\n    }).refine((val)=>[\n            \"td\",\n            \"tp\",\n            \"course\"\n        ].includes(val), {\n        message: \"Invalid type\"\n    }).optional(),\n    \"class_rome_id\": zod__WEBPACK_IMPORTED_MODULE_9__.z.coerce.number().optional(),\n    \"day_id\": zod__WEBPACK_IMPORTED_MODULE_9__.z.coerce.number().optional(),\n    \"module_id\": zod__WEBPACK_IMPORTED_MODULE_9__.z.coerce.number().optional(),\n    \"teacher_id\": zod__WEBPACK_IMPORTED_MODULE_9__.z.coerce.number().optional(),\n    \"group_id\": zod__WEBPACK_IMPORTED_MODULE_9__.z.coerce.number().optional(),\n    \"message\": zod__WEBPACK_IMPORTED_MODULE_9__.z.string().optional()\n});\nfunction TeacherValidClasses() {\n    var _errors_start_time, _errors_end_time, _errors_day, _errors_class_rome_id, _errors_start_time1, _errors_end_time1, _errors_day_id, _errors_group_id, _errors_module_id, _errors_teacher_id, _errors_type, _errors_message;\n    _s();\n    const [groups, setGroups] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)([\n        {\n            id: 1,\n            number: 1\n        },\n        {\n            id: 2,\n            number: 2\n        },\n        {\n            id: 3,\n            number: 3\n        },\n        {\n            id: 4,\n            number: 4\n        },\n        {\n            id: 5,\n            number: 5\n        },\n        {\n            id: 6,\n            number: 6\n        },\n        {\n            id: 7,\n            number: 7\n        }\n    ]);\n    const [sectionId] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(1);\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)([]);\n    const [days, setDays] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)();\n    const [requestSuccess, setRequestSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(false);\n    const [requestError, setRequestError] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_8__.useEffect)({\n        \"TeacherValidClasses.useEffect\": ()=>{\n            const fetchData = {\n                \"TeacherValidClasses.useEffect.fetchData\": async ()=>{\n                    try {\n                        const [daysData] = await Promise.all([\n                            (0,_lib_server_actions_sectionTiming_SectionTimingActions__WEBPACK_IMPORTED_MODULE_6__.getDays)(1)\n                        ]);\n                        setDays(daysData);\n                    } catch (error) {\n                        console.error(\"Error fetching data:\", error);\n                    }\n                }\n            }[\"TeacherValidClasses.useEffect.fetchData\"];\n            fetchData();\n        }\n    }[\"TeacherValidClasses.useEffect\"], []);\n    const { register, handleSubmit, formState: { errors, isSubmitting, isSubmitSuccessful } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__.zodResolver)(FormSchema)\n    });\n    console.log(errors);\n    const onSubmit = async (data)=>{\n        setRequestSuccess(false);\n        setRequestError(null);\n        if ((classes === null || classes === void 0 ? void 0 : classes.length) === 0) {\n            // First step: Search for valid classes\n            const data_payload = {\n                start_time: data.start_time,\n                end_time: data.end_time,\n                day: data.day\n            };\n            try {\n                const response = await (0,_lib_server_actions_sectionTiming_SectionTimingActions__WEBPACK_IMPORTED_MODULE_6__.validClassRoom)(sectionId, data_payload);\n                setClasses(response);\n            } catch (error) {\n                console.error('Error fetching classes:', error);\n                setRequestError('Failed to find valid classes');\n            }\n        } else {\n            // Second step: Submit request to admin\n            if (!data.class_rome_id || !data.module_id || !data.teacher_id || !data.type) {\n                setRequestError('Please fill in all required fields');\n                return;\n            }\n            try {\n                const requestData = {\n                    teacher_id: data.teacher_id,\n                    section_id: sectionId,\n                    group_id: data.group_id,\n                    module_id: data.module_id,\n                    class_rome_id: data.class_rome_id,\n                    day: data.day,\n                    start_time: data.start_time,\n                    end_time: data.end_time,\n                    type: data.type,\n                    message: data.message\n                };\n                const result = await (0,_lib_server_actions_request_requestActions__WEBPACK_IMPORTED_MODULE_7__.createRequest)(requestData);\n                if (result.success) {\n                    setRequestSuccess(true);\n                    setClasses([]); // Reset form\n                } else {\n                    setRequestError(result.message);\n                }\n            } catch (error) {\n                console.error('Error submitting request:', error);\n                setRequestError('Failed to submit request');\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"my-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Buttons_Button__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Loader2_Plus_Send_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 27\n                }, void 0),\n                mode: \"filled\",\n                onClick: ()=>(0,_components_global_Modal_Modal__WEBPACK_IMPORTED_MODULE_2__.openModal)(\"lessen-timing-form\"),\n                children: \"Search Valid Class\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                lineNumber: 151,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Modal_Modal__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                id: \"lessen-timing-form\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-1/3 flex flex-col gap-4 items-center justify-center p-4 bg-surface-container dark:bg-dark-surface-container rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-title-large font-bold text-on-surface dark:text-dark-on-surface\",\n                            children: \"Find A Valid Class\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 21\n                        }, this),\n                        requestSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-2 items-center justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Loader2_Plus_Send_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    size: 24,\n                                    className: \"text-green-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-green-500\",\n                                    children: \"Request sent successfully to admin!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 25\n                        }, this),\n                        requestError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-2 items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-500\",\n                                children: requestError\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 25\n                        }, this),\n                        (classes === null || classes === void 0 ? void 0 : classes.length) > 0 && !requestSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-2 items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-500\",\n                                children: [\n                                    \"Found \",\n                                    classes.length,\n                                    \" available classrooms. Please complete the form to send your request.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit(onSubmit),\n                            className: \"flex flex-col gap-4 items-center justify-center\",\n                            children: [\n                                (classes === null || classes === void 0 ? void 0 : classes.length) === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Inputs_inputs__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            error: (_errors_start_time = errors.start_time) === null || _errors_start_time === void 0 ? void 0 : _errors_start_time.message,\n                                            register: register,\n                                            label: \"start_time\",\n                                            title: \"Start Time\",\n                                            placeholder: \"Start Time (HH:MM:SS)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Inputs_inputs__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            error: (_errors_end_time = errors.end_time) === null || _errors_end_time === void 0 ? void 0 : _errors_end_time.message,\n                                            register: register,\n                                            label: \"end_time\",\n                                            title: \"End Time\",\n                                            placeholder: \"End Time (HH:MM:SS)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Inputs_SimpleSelect__WEBPACK_IMPORTED_MODULE_5__.SimpleSelect, {\n                                            register: register(\"day\"),\n                                            error: (_errors_day = errors.day) === null || _errors_day === void 0 ? void 0 : _errors_day.message,\n                                            label: \"day\",\n                                            title: \"Day\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select a day\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"mon\",\n                                                    children: \"Monday\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"tue\",\n                                                    children: \"Tuesday\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"wed\",\n                                                    children: \"Wednesday\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"thu\",\n                                                    children: \"Thursday\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"fri\",\n                                                    children: \"Friday\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"sat\",\n                                                    children: \"Saturday\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"sun\",\n                                                    children: \"Sunday\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 41\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 37\n                                        }, this)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Inputs_SimpleSelect__WEBPACK_IMPORTED_MODULE_5__.SimpleSelect, {\n                                            register: register(\"class_rome_id\"),\n                                            error: (_errors_class_rome_id = errors.class_rome_id) === null || _errors_class_rome_id === void 0 ? void 0 : _errors_class_rome_id.message,\n                                            label: \"class_rome_id\",\n                                            title: \"Class\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select a class\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 41\n                                                }, this),\n                                                classes === null || classes === void 0 ? void 0 : classes.map((class_)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: class_.id,\n                                                        children: class_.number\n                                                    }, class_.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 45\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Inputs_inputs__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            error: (_errors_start_time1 = errors.start_time) === null || _errors_start_time1 === void 0 ? void 0 : _errors_start_time1.message,\n                                            register: register,\n                                            label: \"start_time\",\n                                            title: \"Start Time\",\n                                            placeholder: \"Start Time (HH:MM:SS)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Inputs_inputs__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            error: (_errors_end_time1 = errors.end_time) === null || _errors_end_time1 === void 0 ? void 0 : _errors_end_time1.message,\n                                            register: register,\n                                            label: \"end_time\",\n                                            title: \"End Time\",\n                                            placeholder: \"End Time (HH:MM:SS)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Inputs_SimpleSelect__WEBPACK_IMPORTED_MODULE_5__.SimpleSelect, {\n                                            register: register(\"day_id\"),\n                                            error: (_errors_day_id = errors.day_id) === null || _errors_day_id === void 0 ? void 0 : _errors_day_id.message,\n                                            label: \"day_id\",\n                                            title: \"Day\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select a day\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 41\n                                                }, this),\n                                                days === null || days === void 0 ? void 0 : days.map((day)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: day.id,\n                                                        children: day.name\n                                                    }, day.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 45\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Inputs_SimpleSelect__WEBPACK_IMPORTED_MODULE_5__.SimpleSelect, {\n                                            register: register(\"group_id\"),\n                                            error: (_errors_group_id = errors.group_id) === null || _errors_group_id === void 0 ? void 0 : _errors_group_id.message,\n                                            label: \"group_id\",\n                                            title: \"Group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select a group\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 41\n                                                }, this),\n                                                groups === null || groups === void 0 ? void 0 : groups.map((group)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: group.id,\n                                                        children: group.number\n                                                    }, group.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 45\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 38\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Inputs_SimpleSelect__WEBPACK_IMPORTED_MODULE_5__.SimpleSelect, {\n                                            register: register(\"module_id\"),\n                                            error: (_errors_module_id = errors.module_id) === null || _errors_module_id === void 0 ? void 0 : _errors_module_id.message,\n                                            label: \"module_id\",\n                                            title: \"Module\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select a module\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"1\",\n                                                    children: \"Mathematics\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"2\",\n                                                    children: \"Physics\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"3\",\n                                                    children: \"Chemistry\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 41\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Inputs_SimpleSelect__WEBPACK_IMPORTED_MODULE_5__.SimpleSelect, {\n                                            register: register(\"teacher_id\"),\n                                            error: (_errors_teacher_id = errors.teacher_id) === null || _errors_teacher_id === void 0 ? void 0 : _errors_teacher_id.message,\n                                            label: \"teacher_id\",\n                                            title: \"Teacher\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select a teacher\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"1\",\n                                                    children: \"Teacher 1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"2\",\n                                                    children: \"Teacher 2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"3\",\n                                                    children: \"Teacher 3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 41\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Inputs_SimpleSelect__WEBPACK_IMPORTED_MODULE_5__.SimpleSelect, {\n                                            register: register(\"type\"),\n                                            error: (_errors_type = errors.type) === null || _errors_type === void 0 ? void 0 : _errors_type.message,\n                                            label: \"type\",\n                                            title: \"Class Type\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select type\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"course\",\n                                                    children: \"Course\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"td\",\n                                                    children: \"TD\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"tp\",\n                                                    children: \"TP\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 41\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Inputs_inputs__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            error: (_errors_message = errors.message) === null || _errors_message === void 0 ? void 0 : _errors_message.message,\n                                            register: register,\n                                            label: \"message\",\n                                            title: \"Message (Optional)\",\n                                            placeholder: \"Additional message for admin...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 37\n                                        }, this)\n                                    ]\n                                }, void 0, true),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full flex justify-end\",\n                                    children: (classes === null || classes === void 0 ? void 0 : classes.length) === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Buttons_Button__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        type: \"submit\",\n                                        icon: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Loader2_Plus_Send_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 80\n                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Loader2_Plus_Send_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 119\n                                        }, void 0),\n                                        mode: \"filled\",\n                                        children: \"Find Classes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 37\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Buttons_Button__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        type: \"submit\",\n                                        icon: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Loader2_Plus_Send_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 80\n                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Loader2_Plus_Send_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 119\n                                        }, void 0),\n                                        mode: \"filled\",\n                                        children: \"Send Request\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 37\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                lineNumber: 154,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n        lineNumber: 150,\n        columnNumber: 9\n    }, this);\n}\n_s(TeacherValidClasses, \"zParkQLHwyICPEHDVLWw6du8ptk=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm\n    ];\n});\n_c = TeacherValidClasses;\nvar _c;\n$RefreshReg$(_c, \"TeacherValidClasses\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/ui/forms/TimingForms/TeacherValidClasses.tsx\n"));

/***/ })

});