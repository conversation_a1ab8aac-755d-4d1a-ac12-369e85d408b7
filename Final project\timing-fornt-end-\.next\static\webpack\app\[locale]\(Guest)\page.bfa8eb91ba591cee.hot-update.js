"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(Guest)/page",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5185115b804f\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHByb1xcRGVza3RvcFxcZmluIHByb2ogY29waWUgMlxcRmluYWwgcHJvamVjdFxcdGltaW5nLWZvcm50LWVuZC1cXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjUxODUxMTViODA0ZlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/ui/forms/TimingForms/TeacherValidClasses.tsx":
/*!**************************************************************!*\
  !*** ./src/lib/ui/forms/TimingForms/TeacherValidClasses.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TeacherValidClasses)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Loader2_Plus_Send_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Loader2,Plus,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Loader2_Plus_Send_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Loader2,Plus,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Loader2_Plus_Send_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Loader2,Plus,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Loader2_Plus_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Loader2,Plus,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _components_global_Buttons_Button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../components/global/Buttons/Button */ \"(app-pages-browser)/./src/lib/ui/components/global/Buttons/Button.tsx\");\n/* harmony import */ var _components_global_Modal_Modal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/global/Modal/Modal */ \"(app-pages-browser)/./src/lib/ui/components/global/Modal/Modal.tsx\");\n/* harmony import */ var _components_global_Inputs_inputs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/global/Inputs/inputs */ \"(app-pages-browser)/./src/lib/ui/components/global/Inputs/inputs.tsx\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _components_global_Inputs_SimpleSelect__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/global/Inputs/SimpleSelect */ \"(app-pages-browser)/./src/lib/ui/components/global/Inputs/SimpleSelect.tsx\");\n/* harmony import */ var _lib_server_actions_sectionTiming_SectionTimingActions__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/server/actions/sectionTiming/SectionTimingActions */ \"(app-pages-browser)/./src/lib/server/actions/sectionTiming/SectionTimingActions.ts\");\n/* harmony import */ var _lib_server_actions_request_requestActions__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/server/actions/request/requestActions */ \"(app-pages-browser)/./src/lib/server/actions/request/requestActions.ts\");\n/* harmony import */ var _lib_hooks_useCurrentUser__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/hooks/useCurrentUser */ \"(app-pages-browser)/./src/lib/hooks/useCurrentUser.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst FormSchema = zod__WEBPACK_IMPORTED_MODULE_10__.z.object({\n    \"start_time\": zod__WEBPACK_IMPORTED_MODULE_10__.z.string(),\n    \"end_time\": zod__WEBPACK_IMPORTED_MODULE_10__.z.string(),\n    \"day\": zod__WEBPACK_IMPORTED_MODULE_10__.z.string({\n        required_error: \"Day is required\"\n    }).refine((val)=>[\n            \"mon\",\n            \"tue\",\n            \"wed\",\n            \"thu\",\n            \"fri\",\n            \"sat\",\n            \"sun\"\n        ].includes(val), {\n        message: \"Invalid day\"\n    }),\n    \"type\": zod__WEBPACK_IMPORTED_MODULE_10__.z.string({\n        required_error: \"Type is required\"\n    }).refine((val)=>[\n            \"td\",\n            \"tp\",\n            \"course\"\n        ].includes(val), {\n        message: \"Invalid type\"\n    }).optional(),\n    \"class_rome_id\": zod__WEBPACK_IMPORTED_MODULE_10__.z.coerce.number().optional(),\n    \"day_id\": zod__WEBPACK_IMPORTED_MODULE_10__.z.coerce.number().optional(),\n    \"module_id\": zod__WEBPACK_IMPORTED_MODULE_10__.z.coerce.number().optional(),\n    \"teacher_id\": zod__WEBPACK_IMPORTED_MODULE_10__.z.coerce.number().optional(),\n    \"group_id\": zod__WEBPACK_IMPORTED_MODULE_10__.z.coerce.number().optional(),\n    \"message\": zod__WEBPACK_IMPORTED_MODULE_10__.z.string().optional()\n});\nfunction TeacherValidClasses() {\n    var _errors_start_time, _errors_end_time, _errors_day, _errors_class_rome_id, _errors_start_time1, _errors_end_time1, _errors_day_id, _errors_group_id, _errors_module_id, _user_key_keyable, _user_key, _user_key_keyable1, _user_key1, _errors_type, _errors_message;\n    _s();\n    const { user, loading: userLoading, error: userError } = (0,_lib_hooks_useCurrentUser__WEBPACK_IMPORTED_MODULE_8__.useCurrentUser)();\n    const [groups, setGroups] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)([\n        {\n            id: 1,\n            number: 1\n        },\n        {\n            id: 2,\n            number: 2\n        },\n        {\n            id: 3,\n            number: 3\n        },\n        {\n            id: 4,\n            number: 4\n        },\n        {\n            id: 5,\n            number: 5\n        },\n        {\n            id: 6,\n            number: 6\n        },\n        {\n            id: 7,\n            number: 7\n        }\n    ]);\n    const [sectionId] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(1);\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)([]);\n    const [days, setDays] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)();\n    const [requestSuccess, setRequestSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    const [requestError, setRequestError] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)({\n        \"TeacherValidClasses.useEffect\": ()=>{\n            const fetchData = {\n                \"TeacherValidClasses.useEffect.fetchData\": async ()=>{\n                    try {\n                        const [daysData] = await Promise.all([\n                            (0,_lib_server_actions_sectionTiming_SectionTimingActions__WEBPACK_IMPORTED_MODULE_6__.getDays)(1)\n                        ]);\n                        setDays(daysData);\n                    } catch (error) {\n                        console.error(\"Error fetching data:\", error);\n                    }\n                }\n            }[\"TeacherValidClasses.useEffect.fetchData\"];\n            fetchData();\n        }\n    }[\"TeacherValidClasses.useEffect\"], []);\n    const { register, handleSubmit, formState: { errors, isSubmitting, isSubmitSuccessful } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_11__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__.zodResolver)(FormSchema)\n    });\n    console.log(errors);\n    const onSubmit = async (data)=>{\n        setRequestSuccess(false);\n        setRequestError(null);\n        if ((classes === null || classes === void 0 ? void 0 : classes.length) === 0) {\n            // First step: Search for valid classes\n            const data_payload = {\n                start_time: data.start_time,\n                end_time: data.end_time,\n                day: data.day\n            };\n            try {\n                const response = await (0,_lib_server_actions_sectionTiming_SectionTimingActions__WEBPACK_IMPORTED_MODULE_6__.validClassRoom)(sectionId, data_payload);\n                setClasses(response);\n            } catch (error) {\n                console.error('Error fetching classes:', error);\n                setRequestError('Failed to find valid classes');\n            }\n        } else {\n            // Second step: Submit request to admin\n            if (!data.class_rome_id || !data.module_id || !data.type) {\n                setRequestError('Please fill in all required fields');\n                return;\n            }\n            if (!user || user.key.keyable_type !== 'teacher') {\n                setRequestError('You must be logged in as a teacher to submit requests');\n                return;\n            }\n            try {\n                const requestData = {\n                    teacher_id: user.key.keyable.id,\n                    section_id: sectionId,\n                    group_id: data.group_id,\n                    module_id: data.module_id,\n                    class_rome_id: data.class_rome_id,\n                    day: data.day,\n                    start_time: data.start_time,\n                    end_time: data.end_time,\n                    type: data.type,\n                    message: data.message\n                };\n                const result = await (0,_lib_server_actions_request_requestActions__WEBPACK_IMPORTED_MODULE_7__.createRequest)(requestData);\n                if (result.success) {\n                    setRequestSuccess(true);\n                    setClasses([]); // Reset form\n                } else {\n                    setRequestError(result.message);\n                }\n            } catch (error) {\n                console.error('Error submitting request:', error);\n                setRequestError('Failed to submit request');\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"my-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Buttons_Button__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Loader2_Plus_Send_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 27\n                }, void 0),\n                mode: \"filled\",\n                onClick: ()=>(0,_components_global_Modal_Modal__WEBPACK_IMPORTED_MODULE_2__.openModal)(\"lessen-timing-form\"),\n                children: \"Search Valid Class\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                lineNumber: 157,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Modal_Modal__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                id: \"lessen-timing-form\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-1/3 flex flex-col gap-4 items-center justify-center p-4 bg-surface-container dark:bg-dark-surface-container rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-title-large font-bold text-on-surface dark:text-dark-on-surface\",\n                            children: \"Find A Valid Class\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 21\n                        }, this),\n                        requestSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-2 items-center justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Loader2_Plus_Send_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    size: 24,\n                                    className: \"text-green-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-green-500\",\n                                    children: \"Request sent successfully to admin!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 25\n                        }, this),\n                        requestError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-2 items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-500\",\n                                children: requestError\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 25\n                        }, this),\n                        (classes === null || classes === void 0 ? void 0 : classes.length) > 0 && !requestSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-2 items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-500\",\n                                children: [\n                                    \"Found \",\n                                    classes.length,\n                                    \" available classrooms. Please complete the form to send your request.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit(onSubmit),\n                            className: \"flex flex-col gap-4 items-center justify-center\",\n                            children: [\n                                (classes === null || classes === void 0 ? void 0 : classes.length) === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Inputs_inputs__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            error: (_errors_start_time = errors.start_time) === null || _errors_start_time === void 0 ? void 0 : _errors_start_time.message,\n                                            register: register,\n                                            label: \"start_time\",\n                                            title: \"Start Time\",\n                                            placeholder: \"Start Time (HH:MM:SS)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Inputs_inputs__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            error: (_errors_end_time = errors.end_time) === null || _errors_end_time === void 0 ? void 0 : _errors_end_time.message,\n                                            register: register,\n                                            label: \"end_time\",\n                                            title: \"End Time\",\n                                            placeholder: \"End Time (HH:MM:SS)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Inputs_SimpleSelect__WEBPACK_IMPORTED_MODULE_5__.SimpleSelect, {\n                                            register: register(\"day\"),\n                                            error: (_errors_day = errors.day) === null || _errors_day === void 0 ? void 0 : _errors_day.message,\n                                            label: \"day\",\n                                            title: \"Day\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select a day\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"mon\",\n                                                    children: \"Monday\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"tue\",\n                                                    children: \"Tuesday\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"wed\",\n                                                    children: \"Wednesday\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"thu\",\n                                                    children: \"Thursday\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"fri\",\n                                                    children: \"Friday\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"sat\",\n                                                    children: \"Saturday\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"sun\",\n                                                    children: \"Sunday\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 41\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 37\n                                        }, this)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Inputs_SimpleSelect__WEBPACK_IMPORTED_MODULE_5__.SimpleSelect, {\n                                            register: register(\"class_rome_id\"),\n                                            error: (_errors_class_rome_id = errors.class_rome_id) === null || _errors_class_rome_id === void 0 ? void 0 : _errors_class_rome_id.message,\n                                            label: \"class_rome_id\",\n                                            title: \"Class\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select a class\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 41\n                                                }, this),\n                                                classes === null || classes === void 0 ? void 0 : classes.map((class_)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: class_.id,\n                                                        children: class_.number\n                                                    }, class_.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 45\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Inputs_inputs__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            error: (_errors_start_time1 = errors.start_time) === null || _errors_start_time1 === void 0 ? void 0 : _errors_start_time1.message,\n                                            register: register,\n                                            label: \"start_time\",\n                                            title: \"Start Time\",\n                                            placeholder: \"Start Time (HH:MM:SS)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Inputs_inputs__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            error: (_errors_end_time1 = errors.end_time) === null || _errors_end_time1 === void 0 ? void 0 : _errors_end_time1.message,\n                                            register: register,\n                                            label: \"end_time\",\n                                            title: \"End Time\",\n                                            placeholder: \"End Time (HH:MM:SS)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Inputs_SimpleSelect__WEBPACK_IMPORTED_MODULE_5__.SimpleSelect, {\n                                            register: register(\"day_id\"),\n                                            error: (_errors_day_id = errors.day_id) === null || _errors_day_id === void 0 ? void 0 : _errors_day_id.message,\n                                            label: \"day_id\",\n                                            title: \"Day\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select a day\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 41\n                                                }, this),\n                                                days === null || days === void 0 ? void 0 : days.map((day)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: day.id,\n                                                        children: day.name\n                                                    }, day.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 45\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Inputs_SimpleSelect__WEBPACK_IMPORTED_MODULE_5__.SimpleSelect, {\n                                            register: register(\"group_id\"),\n                                            error: (_errors_group_id = errors.group_id) === null || _errors_group_id === void 0 ? void 0 : _errors_group_id.message,\n                                            label: \"group_id\",\n                                            title: \"Group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select a group\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 41\n                                                }, this),\n                                                groups === null || groups === void 0 ? void 0 : groups.map((group)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: group.id,\n                                                        children: group.number\n                                                    }, group.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 45\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 38\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Inputs_SimpleSelect__WEBPACK_IMPORTED_MODULE_5__.SimpleSelect, {\n                                            register: register(\"module_id\"),\n                                            error: (_errors_module_id = errors.module_id) === null || _errors_module_id === void 0 ? void 0 : _errors_module_id.message,\n                                            label: \"module_id\",\n                                            title: \"Module\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select a module\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"1\",\n                                                    children: \"Mathematics\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"2\",\n                                                    children: \"Physics\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"3\",\n                                                    children: \"Chemistry\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 41\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: \"Teacher:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 41\n                                                }, this),\n                                                \" \",\n                                                user === null || user === void 0 ? void 0 : (_user_key = user.key) === null || _user_key === void 0 ? void 0 : (_user_key_keyable = _user_key.keyable) === null || _user_key_keyable === void 0 ? void 0 : _user_key_keyable.name,\n                                                \" \",\n                                                user === null || user === void 0 ? void 0 : (_user_key1 = user.key) === null || _user_key1 === void 0 ? void 0 : (_user_key_keyable1 = _user_key1.keyable) === null || _user_key_keyable1 === void 0 ? void 0 : _user_key_keyable1.last\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Inputs_SimpleSelect__WEBPACK_IMPORTED_MODULE_5__.SimpleSelect, {\n                                            register: register(\"type\"),\n                                            error: (_errors_type = errors.type) === null || _errors_type === void 0 ? void 0 : _errors_type.message,\n                                            label: \"type\",\n                                            title: \"Class Type\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select type\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"course\",\n                                                    children: \"Course\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"td\",\n                                                    children: \"TD\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"tp\",\n                                                    children: \"TP\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 41\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Inputs_inputs__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            error: (_errors_message = errors.message) === null || _errors_message === void 0 ? void 0 : _errors_message.message,\n                                            register: register,\n                                            label: \"message\",\n                                            title: \"Message (Optional)\",\n                                            placeholder: \"Additional message for admin...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 37\n                                        }, this)\n                                    ]\n                                }, void 0, true),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full flex justify-end\",\n                                    children: (classes === null || classes === void 0 ? void 0 : classes.length) === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Buttons_Button__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        type: \"submit\",\n                                        icon: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Loader2_Plus_Send_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 80\n                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Loader2_Plus_Send_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 119\n                                        }, void 0),\n                                        mode: \"filled\",\n                                        children: \"Find Classes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 37\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Buttons_Button__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        type: \"submit\",\n                                        icon: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Loader2_Plus_Send_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 80\n                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Loader2_Plus_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 119\n                                        }, void 0),\n                                        mode: \"filled\",\n                                        children: \"Send Request\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 37\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n                lineNumber: 160,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie 2\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\forms\\\\TimingForms\\\\TeacherValidClasses.tsx\",\n        lineNumber: 156,\n        columnNumber: 9\n    }, this);\n}\n_s(TeacherValidClasses, \"paaNTivynVpUQtFi9YLHLteznbM=\", false, function() {\n    return [\n        _lib_hooks_useCurrentUser__WEBPACK_IMPORTED_MODULE_8__.useCurrentUser,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_11__.useForm\n    ];\n});\n_c = TeacherValidClasses;\nvar _c;\n$RefreshReg$(_c, \"TeacherValidClasses\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/ui/forms/TimingForms/TeacherValidClasses.tsx\n"));

/***/ })

});