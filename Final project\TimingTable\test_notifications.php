<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

// Get all users
$users = App\Models\User::with('key')->get();

echo "=== USERS IN DATABASE ===\n";
foreach ($users as $user) {
    echo "ID: {$user->id}, Email: {$user->email}, Type: {$user->key->keyable_type}\n";
}

// Get all notifications
$notifications = App\Models\Api\Main\Notification::with('user')->get();

echo "\n=== NOTIFICATIONS IN DATABASE ===\n";
echo "Total notifications: " . $notifications->count() . "\n";

foreach ($notifications as $notification) {
    echo "ID: {$notification->id}, User: {$notification->user_id} ({$notification->user->email}), Type: {$notification->type}, Title: {$notification->title}, Read: " . ($notification->read ? 'Yes' : 'No') . "\n";
}

// Create a test notification for the first teacher
$teacher = App\Models\User::whereHas('key', function($query) {
    $query->where('keyable_type', 'teacher');
})->first();

if ($teacher) {
    echo "\n=== CREATING TEST NOTIFICATION ===\n";
    $notification = App\Models\Api\Main\Notification::create([
        'user_id' => $teacher->id,
        'type' => 'request_approved',
        'title' => 'Test Notification',
        'message' => 'This is a test notification to verify the system is working.',
        'data' => json_encode(['test' => true]),
        'read' => false
    ]);
    
    echo "Created notification ID: {$notification->id} for teacher {$teacher->email}\n";
} else {
    echo "\n=== NO TEACHER FOUND ===\n";
}

echo "\n=== FINAL NOTIFICATION COUNT ===\n";
echo "Total notifications: " . App\Models\Api\Main\Notification::count() . "\n";
