"use client";
import { useState } from "react";
import { useRouter } from "next/navigation";
import Button from "@/lib/ui/components/global/Buttons/Button";
import Dialog from "@/lib/ui/components/global/Dialog/Dialog";
import CreateDepartmentForm from "@/lib/ui/forms/department/CreateDepartmentForm";
import { Plus } from "lucide-react";

export default function CreateDepartmentDialog() {
    const [open, setOpen] = useState(false);
    const router = useRouter();

    const handleSuccess = () => {
        // Add a small delay to ensure server-side revalidation completes
        setTimeout(() => {
            setOpen(false);
            router.refresh();
        }, 100);
    };

    return (
        <>
            <Button mode="filled" icon={<Plus />} onClick={() => setOpen(true)}>
                Create Department
            </Button>
            <Dialog isOpen={open} onClose={() => setOpen(false)} title="Create Department">
                <CreateDepartmentForm onSuccess={handleSuccess} />
            </Dialog>
        </>
    );
}
