'use server'

import { TeacherResponse } from '@/lib/server/types/teacher/teacher'
import axiosInstance from '@/lib/server/tools/axios'

export async function getTeachers(page: number = 1, search: string = ''): Promise<TeacherResponse> {
    try {
        const searchParam = search ? `&search=${encodeURIComponent(search)}` : '';
        const { data } = await axiosInstance.get<TeacherResponse>(`/teachers?page=${page}${searchParam}`)
        return data
    } catch (error: any) {
        console.error('Error fetching teachers:', error.response?.data)
        throw error.response?.data
    }
} 