import { TeacherResponse } from "@/lib/server/types/teacher/teacher"
import { DashContentPagination, DashContentPaginationItem } from "../DashCrudContent"
import { getTeachers } from "@/lib/server/actions/teacher/getTeachers";

interface TeacherPaginationProps {
    data: TeacherResponse;
    currentPage: number;
    search: string;
}

export default async function TeacherPagination({ currentPage, search }: TeacherPaginationProps) {
    const data = await getTeachers(currentPage, search);

    return (
        <DashContentPagination>
            {/* Previous button */}
            {data.prev_page_url && (
                <DashContentPaginationItem 
                    href={`/dashboard/teachers?page=${currentPage - 1}${search ? `&search=${search}` : ''}`}
                >
                    Previous
                </DashContentPaginationItem>
            )}

            {/* Page numbers */}
            {data.links.slice(1, -1).map((link, index) => (
                link.url && (
                    <DashContentPaginationItem 
                        key={index} 
                        href={`/dashboard/teachers?page=${index + 1}${search ? `&search=${search}` : ''}${currentPage === index + 1 ? '&active' : ''}`}
                    >
                        {link.label}
                    </DashContentPaginationItem>
                )
            ))}

            {/* Next button */}
            {data.next_page_url && (
                <DashContentPaginationItem 
                    href={`/dashboard/teachers?page=${currentPage + 1}${search ? `&search=${search}` : ''}`}
                >
                    Next
                </DashContentPaginationItem>
            )}
        </DashContentPagination>
    )
}