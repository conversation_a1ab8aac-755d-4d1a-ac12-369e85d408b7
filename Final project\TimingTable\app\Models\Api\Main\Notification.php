<?php

namespace App\Models\Api\Main;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Notification extends Model
{
    protected $fillable = [
        'user_id',
        'type',
        'title',
        'message',
        'data',
        'read',
        'read_at',
    ];

    protected $casts = [
        'data' => 'array',
        'read' => 'boolean',
        'read_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function scopeUnread($query)
    {
        return $query->where('read', false);
    }

    public function scopeRead($query)
    {
        return $query->where('read', true);
    }

    public function markAsRead()
    {
        $this->update([
            'read' => true,
            'read_at' => now(),
        ]);
    }

    public static function createForUser($userId, $type, $title, $message, $data = null)
    {
        return static::create([
            'user_id' => $userId,
            'type' => $type,
            'title' => $title,
            'message' => $message,
            'data' => $data,
        ]);
    }

    public static function createRequestApproved($teacherUserId, $requestId, $lessonId = null)
    {
        return static::createForUser(
            $teacherUserId,
            'request_approved',
            'Request Approved',
            'Your classroom request has been approved and the lesson has been created.',
            [
                'request_id' => $requestId,
                'lesson_id' => $lessonId,
            ]
        );
    }

    public static function createRequestRejected($teacherUserId, $requestId, $reason = null)
    {
        return static::createForUser(
            $teacherUserId,
            'request_rejected',
            'Request Rejected',
            $reason ?: 'Your classroom request has been rejected.',
            [
                'request_id' => $requestId,
            ]
        );
    }
}
