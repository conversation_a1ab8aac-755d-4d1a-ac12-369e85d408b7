globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/[locale]/(Dashboard)/dashboard/(timing)/groups/timing/[group]/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(ssr)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js":{"*":{"id":"(ssr)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/ui/components/global/Navigations/NavBar/NavBarItem.tsx":{"*":{"id":"(ssr)/./src/lib/ui/components/global/Navigations/NavBar/NavBarItem.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/ui/components/local/Mode.tsx":{"*":{"id":"(ssr)/./src/lib/ui/components/local/Mode.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/ui/components/local/Welcom page/Logout.tsx":{"*":{"id":"(ssr)/./src/lib/ui/components/local/Welcom page/Logout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/(Guest)/(timing)/student/groupTimng.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/(Guest)/(timing)/student/groupTimng.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/(Guest)/(timing)/student/PrintButton.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/(Guest)/(timing)/student/PrintButton.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/(Guest)/(timing)/student/sectionTimng.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/(Guest)/(timing)/student/sectionTimng.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/(Guest)/(timing)/teacher/index.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/(Guest)/(timing)/teacher/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/ui/forms/auth/login/LoginForm.tsx":{"*":{"id":"(ssr)/./src/lib/ui/forms/auth/login/LoginForm.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/ui/components/global/Navigations/Navigation/NavItem.tsx":{"*":{"id":"(ssr)/./src/lib/ui/components/global/Navigations/Navigation/NavItem.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/ui/components/local/Dashboard/Request.tsx":{"*":{"id":"(ssr)/./src/lib/ui/components/local/Dashboard/Request.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/ui/components/local/Dashboard/TeacherNotifications.tsx":{"*":{"id":"(ssr)/./src/lib/ui/components/local/Dashboard/TeacherNotifications.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/ui/components/global/Buttons/Button.tsx":{"*":{"id":"(ssr)/./src/lib/ui/components/global/Buttons/Button.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/ui/forms/admin/createKey.tsx":{"*":{"id":"(ssr)/./src/lib/ui/forms/admin/createKey.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(core)/modules/CreateModuleDialog.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/(Dashboard)/dashboard/(core)/modules/CreateModuleDialog.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(core)/years/CreateYearDialog.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/(Dashboard)/dashboard/(core)/years/CreateYearDialog.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(main)/students/SearchStudents.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/(Dashboard)/dashboard/(main)/students/SearchStudents.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(main)/students/StudentDialog.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/(Dashboard)/dashboard/(main)/students/StudentDialog.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/ui/forms/student/createKey.tsx":{"*":{"id":"(ssr)/./src/lib/ui/forms/student/createKey.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/ui/forms/student/delete.tsx":{"*":{"id":"(ssr)/./src/lib/ui/forms/student/delete.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(timing)/groups/CreateGroupDialog.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/(Dashboard)/dashboard/(timing)/groups/CreateGroupDialog.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(timing)/groups/YearFilter.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/(Dashboard)/dashboard/(timing)/groups/YearFilter.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/ui/forms/group/delete.tsx":{"*":{"id":"(ssr)/./src/lib/ui/forms/group/delete.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(timing)/groups/timing/[group]/timingTable.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/(Dashboard)/dashboard/(timing)/groups/timing/[group]/timingTable.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(timing)/sections/CreateSectionDialog.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/(Dashboard)/dashboard/(timing)/sections/CreateSectionDialog.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/ui/forms/section/delete.tsx":{"*":{"id":"(ssr)/./src/lib/ui/forms/section/delete.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(main)/teachers/SearchTeachers.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/(Dashboard)/dashboard/(main)/teachers/SearchTeachers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(main)/teachers/TeacherDialog.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/(Dashboard)/dashboard/(main)/teachers/TeacherDialog.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/ui/forms/teacher/createKey.tsx":{"*":{"id":"(ssr)/./src/lib/ui/forms/teacher/createKey.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/ui/forms/teacher/delete.tsx":{"*":{"id":"(ssr)/./src/lib/ui/forms/teacher/delete.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(core)/departements/CreateDepartmentDialog.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/(Dashboard)/dashboard/(core)/departements/CreateDepartmentDialog.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\node_modules\\next-intl\\dist\\esm\\development\\shared\\NextIntlClientProvider.js":{"id":"(app-pages-browser)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js","name":"*","chunks":["app/[locale]/(Dashboard)/layout","static/chunks/app/%5Blocale%5D/(Dashboard)/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\[locale]\\\\layout.tsx\",\"import\":\"Cairo\",\"arguments\":[{\"subsets\":[\"arabic\",\"latin\"],\"display\":\"swap\",\"variable\":\"--font-cairo\",\"preload\":true,\"weight\":[\"200\",\"300\",\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"]}],\"variableName\":\"cairo\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\[locale]\\\\layout.tsx\",\"import\":\"Cairo\",\"arguments\":[{\"subsets\":[\"arabic\",\"latin\"],\"display\":\"swap\",\"variable\":\"--font-cairo\",\"preload\":true,\"weight\":[\"200\",\"300\",\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"]}],\"variableName\":\"cairo\"}","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\node_modules\\next\\dist\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/[locale]/(Guest)/layout","static/chunks/app/%5Blocale%5D/(Guest)/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\node_modules\\next\\dist\\esm\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/[locale]/(Guest)/layout","static/chunks/app/%5Blocale%5D/(Guest)/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\lib\\ui\\components\\global\\Navigations\\NavBar\\NavBarItem.tsx":{"id":"(app-pages-browser)/./src/lib/ui/components/global/Navigations/NavBar/NavBarItem.tsx","name":"*","chunks":["app/[locale]/(Dashboard)/layout","static/chunks/app/%5Blocale%5D/(Dashboard)/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\lib\\ui\\components\\local\\Mode.tsx":{"id":"(app-pages-browser)/./src/lib/ui/components/local/Mode.tsx","name":"*","chunks":["app/[locale]/(Dashboard)/layout","static/chunks/app/%5Blocale%5D/(Dashboard)/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\lib\\ui\\components\\local\\Welcom page\\Logout.tsx":{"id":"(app-pages-browser)/./src/lib/ui/components/local/Welcom page/Logout.tsx","name":"*","chunks":["app/[locale]/(Guest)/layout","static/chunks/app/%5Blocale%5D/(Guest)/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\app\\[locale]\\(Guest)\\(timing)\\student\\groupTimng.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/(Guest)/(timing)/student/groupTimng.tsx","name":"*","chunks":["app/[locale]/(Guest)/page","static/chunks/app/%5Blocale%5D/(Guest)/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\app\\[locale]\\(Guest)\\(timing)\\student\\PrintButton.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/(Guest)/(timing)/student/PrintButton.tsx","name":"*","chunks":["app/[locale]/(Guest)/page","static/chunks/app/%5Blocale%5D/(Guest)/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\app\\[locale]\\(Guest)\\(timing)\\student\\sectionTimng.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/(Guest)/(timing)/student/sectionTimng.tsx","name":"*","chunks":["app/[locale]/(Guest)/page","static/chunks/app/%5Blocale%5D/(Guest)/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\app\\[locale]\\(Guest)\\(timing)\\teacher\\index.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/(Guest)/(timing)/teacher/index.tsx","name":"*","chunks":["app/[locale]/(Guest)/page","static/chunks/app/%5Blocale%5D/(Guest)/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\lib\\ui\\forms\\auth\\login\\LoginForm.tsx":{"id":"(app-pages-browser)/./src/lib/ui/forms/auth/login/LoginForm.tsx","name":"*","chunks":["app/[locale]/(Guest)/page","static/chunks/app/%5Blocale%5D/(Guest)/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\node_modules\\next\\dist\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/[locale]/(Dashboard)/dashboard/(timing)/groups/page","static/chunks/app/%5Blocale%5D/(Dashboard)/dashboard/(timing)/groups/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\node_modules\\next\\dist\\esm\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/[locale]/(Dashboard)/dashboard/(timing)/groups/page","static/chunks/app/%5Blocale%5D/(Dashboard)/dashboard/(timing)/groups/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\lib\\ui\\components\\global\\Navigations\\Navigation\\NavItem.tsx":{"id":"(app-pages-browser)/./src/lib/ui/components/global/Navigations/Navigation/NavItem.tsx","name":"*","chunks":["app/[locale]/(Dashboard)/layout","static/chunks/app/%5Blocale%5D/(Dashboard)/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\lib\\ui\\components\\local\\Dashboard\\Request.tsx":{"id":"(app-pages-browser)/./src/lib/ui/components/local/Dashboard/Request.tsx","name":"*","chunks":["app/[locale]/(Dashboard)/layout","static/chunks/app/%5Blocale%5D/(Dashboard)/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\lib\\ui\\components\\local\\Dashboard\\TeacherNotifications.tsx":{"id":"(app-pages-browser)/./src/lib/ui/components/local/Dashboard/TeacherNotifications.tsx","name":"*","chunks":["app/[locale]/(Dashboard)/layout","static/chunks/app/%5Blocale%5D/(Dashboard)/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\lib\\ui\\components\\global\\Buttons\\Button.tsx":{"id":"(app-pages-browser)/./src/lib/ui/components/global/Buttons/Button.tsx","name":"*","chunks":["app/[locale]/(Dashboard)/dashboard/(timing)/groups/page","static/chunks/app/%5Blocale%5D/(Dashboard)/dashboard/(timing)/groups/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\lib\\ui\\forms\\admin\\createKey.tsx":{"id":"(app-pages-browser)/./src/lib/ui/forms/admin/createKey.tsx","name":"*","chunks":["app/[locale]/(Dashboard)/dashboard/(main)/page","static/chunks/app/%5Blocale%5D/(Dashboard)/dashboard/(main)/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\app\\[locale]\\(Dashboard)\\dashboard\\(core)\\modules\\CreateModuleDialog.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(core)/modules/CreateModuleDialog.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\app\\[locale]\\(Dashboard)\\dashboard\\(core)\\years\\CreateYearDialog.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(core)/years/CreateYearDialog.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\app\\[locale]\\(Dashboard)\\dashboard\\(main)\\students\\SearchStudents.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(main)/students/SearchStudents.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\app\\[locale]\\(Dashboard)\\dashboard\\(main)\\students\\StudentDialog.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(main)/students/StudentDialog.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\lib\\ui\\forms\\student\\createKey.tsx":{"id":"(app-pages-browser)/./src/lib/ui/forms/student/createKey.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\lib\\ui\\forms\\student\\delete.tsx":{"id":"(app-pages-browser)/./src/lib/ui/forms/student/delete.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\app\\[locale]\\(Dashboard)\\dashboard\\(timing)\\groups\\CreateGroupDialog.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(timing)/groups/CreateGroupDialog.tsx","name":"*","chunks":["app/[locale]/(Dashboard)/dashboard/(timing)/groups/page","static/chunks/app/%5Blocale%5D/(Dashboard)/dashboard/(timing)/groups/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\app\\[locale]\\(Dashboard)\\dashboard\\(timing)\\groups\\YearFilter.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(timing)/groups/YearFilter.tsx","name":"*","chunks":["app/[locale]/(Dashboard)/dashboard/(timing)/groups/page","static/chunks/app/%5Blocale%5D/(Dashboard)/dashboard/(timing)/groups/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\lib\\ui\\forms\\group\\delete.tsx":{"id":"(app-pages-browser)/./src/lib/ui/forms/group/delete.tsx","name":"*","chunks":["app/[locale]/(Dashboard)/dashboard/(timing)/groups/page","static/chunks/app/%5Blocale%5D/(Dashboard)/dashboard/(timing)/groups/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\app\\[locale]\\(Dashboard)\\dashboard\\(timing)\\groups\\timing\\[group]\\timingTable.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(timing)/groups/timing/[group]/timingTable.tsx","name":"*","chunks":["app/[locale]/(Dashboard)/dashboard/(timing)/groups/timing/[group]/page","static/chunks/app/%5Blocale%5D/(Dashboard)/dashboard/(timing)/groups/timing/%5Bgroup%5D/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\app\\[locale]\\(Dashboard)\\dashboard\\(timing)\\sections\\CreateSectionDialog.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(timing)/sections/CreateSectionDialog.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\lib\\ui\\forms\\section\\delete.tsx":{"id":"(app-pages-browser)/./src/lib/ui/forms/section/delete.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\app\\[locale]\\(Dashboard)\\dashboard\\(main)\\teachers\\SearchTeachers.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(main)/teachers/SearchTeachers.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\app\\[locale]\\(Dashboard)\\dashboard\\(main)\\teachers\\TeacherDialog.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(main)/teachers/TeacherDialog.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\lib\\ui\\forms\\teacher\\createKey.tsx":{"id":"(app-pages-browser)/./src/lib/ui/forms/teacher/createKey.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\lib\\ui\\forms\\teacher\\delete.tsx":{"id":"(app-pages-browser)/./src/lib/ui/forms/teacher/delete.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\app\\[locale]\\(Dashboard)\\dashboard\\(core)\\departements\\CreateDepartmentDialog.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(core)/departements/CreateDepartmentDialog.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\":[],"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\app\\[locale]\\layout":[{"inlined":false,"path":"static/css/app/[locale]/layout.css"}],"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\app\\[locale]\\(Guest)\\layout":[],"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\app\\[locale]\\(Guest)\\page":[{"inlined":false,"path":"static/css/app/[locale]/(Guest)/page.css"}],"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\app\\[locale]\\(Dashboard)\\layout":[{"inlined":false,"path":"static/css/app/[locale]/(Dashboard)/layout.css"}],"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\app\\[locale]\\(Dashboard)\\dashboard\\(main)\\page":[{"inlined":false,"path":"static/css/app/[locale]/(Dashboard)/dashboard/(main)/page.css"}],"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\app\\[locale]\\(Dashboard)\\dashboard\\(timing)\\groups\\page":[{"inlined":false,"path":"static/css/app/[locale]/(Dashboard)/dashboard/(timing)/groups/page.css"}],"C:\\Users\\<USER>\\Desktop\\fin proj copie 2\\Final project\\timing-fornt-end-\\src\\app\\[locale]\\(Dashboard)\\dashboard\\(timing)\\groups\\timing\\[group]\\page":[{"inlined":false,"path":"static/css/app/[locale]/(Dashboard)/dashboard/(timing)/groups/timing/[group]/page.css"}]},"rscModuleMapping":{"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js":{"*":{"id":"(rsc)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/ui/components/global/Navigations/NavBar/NavBarItem.tsx":{"*":{"id":"(rsc)/./src/lib/ui/components/global/Navigations/NavBar/NavBarItem.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/ui/components/local/Mode.tsx":{"*":{"id":"(rsc)/./src/lib/ui/components/local/Mode.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/ui/components/local/Welcom page/Logout.tsx":{"*":{"id":"(rsc)/./src/lib/ui/components/local/Welcom page/Logout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/(Guest)/(timing)/student/groupTimng.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/(Guest)/(timing)/student/groupTimng.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/(Guest)/(timing)/student/PrintButton.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/(Guest)/(timing)/student/PrintButton.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/(Guest)/(timing)/student/sectionTimng.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/(Guest)/(timing)/student/sectionTimng.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/(Guest)/(timing)/teacher/index.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/(Guest)/(timing)/teacher/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/ui/forms/auth/login/LoginForm.tsx":{"*":{"id":"(rsc)/./src/lib/ui/forms/auth/login/LoginForm.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/ui/components/global/Navigations/Navigation/NavItem.tsx":{"*":{"id":"(rsc)/./src/lib/ui/components/global/Navigations/Navigation/NavItem.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/ui/components/local/Dashboard/Request.tsx":{"*":{"id":"(rsc)/./src/lib/ui/components/local/Dashboard/Request.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/ui/components/local/Dashboard/TeacherNotifications.tsx":{"*":{"id":"(rsc)/./src/lib/ui/components/local/Dashboard/TeacherNotifications.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/ui/components/global/Buttons/Button.tsx":{"*":{"id":"(rsc)/./src/lib/ui/components/global/Buttons/Button.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/ui/forms/admin/createKey.tsx":{"*":{"id":"(rsc)/./src/lib/ui/forms/admin/createKey.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(core)/modules/CreateModuleDialog.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/(Dashboard)/dashboard/(core)/modules/CreateModuleDialog.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(core)/years/CreateYearDialog.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/(Dashboard)/dashboard/(core)/years/CreateYearDialog.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(main)/students/SearchStudents.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/(Dashboard)/dashboard/(main)/students/SearchStudents.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(main)/students/StudentDialog.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/(Dashboard)/dashboard/(main)/students/StudentDialog.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/ui/forms/student/createKey.tsx":{"*":{"id":"(rsc)/./src/lib/ui/forms/student/createKey.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/ui/forms/student/delete.tsx":{"*":{"id":"(rsc)/./src/lib/ui/forms/student/delete.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(timing)/groups/CreateGroupDialog.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/(Dashboard)/dashboard/(timing)/groups/CreateGroupDialog.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(timing)/groups/YearFilter.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/(Dashboard)/dashboard/(timing)/groups/YearFilter.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/ui/forms/group/delete.tsx":{"*":{"id":"(rsc)/./src/lib/ui/forms/group/delete.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(timing)/groups/timing/[group]/timingTable.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/(Dashboard)/dashboard/(timing)/groups/timing/[group]/timingTable.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(timing)/sections/CreateSectionDialog.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/(Dashboard)/dashboard/(timing)/sections/CreateSectionDialog.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/ui/forms/section/delete.tsx":{"*":{"id":"(rsc)/./src/lib/ui/forms/section/delete.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(main)/teachers/SearchTeachers.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/(Dashboard)/dashboard/(main)/teachers/SearchTeachers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(main)/teachers/TeacherDialog.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/(Dashboard)/dashboard/(main)/teachers/TeacherDialog.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/ui/forms/teacher/createKey.tsx":{"*":{"id":"(rsc)/./src/lib/ui/forms/teacher/createKey.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/ui/forms/teacher/delete.tsx":{"*":{"id":"(rsc)/./src/lib/ui/forms/teacher/delete.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(core)/departements/CreateDepartmentDialog.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/(Dashboard)/dashboard/(core)/departements/CreateDepartmentDialog.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}