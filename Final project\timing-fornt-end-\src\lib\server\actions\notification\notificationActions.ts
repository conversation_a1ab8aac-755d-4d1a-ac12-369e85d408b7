'use client'

import clientAxiosInstance from '@/lib/client/axios'

export interface NotificationResponse {
    id: number
    user_id: number
    type: string
    title: string
    message: string
    data: any
    read: boolean
    read_at: string | null
    created_at: string
    updated_at: string
}

export interface NotificationsListResponse {
    data: NotificationResponse[]
    current_page: number
    last_page: number
    per_page: number
    total: number
}

/**
 * Get user notifications
 */
export async function getNotifications(page: number = 1, unreadOnly: boolean = false): Promise<NotificationsListResponse> {
    try {
        const params = new URLSearchParams({
            page: page.toString(),
        })
        
        if (unreadOnly) {
            params.append('unread_only', '1')
        }

        const { data } = await clientAxiosInstance.get<NotificationsListResponse>(
            `/notifications?${params.toString()}`
        )
        
        return data
    } catch (error) {
        console.error('Error fetching notifications:', error)
        throw error
    }
}

/**
 * Get unread notifications count
 */
export async function getUnreadNotificationsCount(): Promise<number> {
    try {
        const { data } = await clientAxiosInstance.get<{ count: number }>('/notifications-unread-count')
        return data.count
    } catch (error) {
        console.error('Error fetching unread notifications count:', error)
        return 0
    }
}

/**
 * Mark notification as read
 */
export async function markNotificationAsRead(notificationId: number): Promise<{ success: boolean; message: string }> {
    try {
        const { data } = await clientAxiosInstance.patch(`/notifications/${notificationId}/read`)
        return { success: true, message: data.message }
    } catch (error: any) {
        console.error('Error marking notification as read:', error)
        return { 
            success: false, 
            message: error.response?.data?.message || 'Failed to mark notification as read' 
        }
    }
}

/**
 * Mark all notifications as read
 */
export async function markAllNotificationsAsRead(): Promise<{ success: boolean; message: string }> {
    try {
        const { data } = await clientAxiosInstance.patch('/notifications/mark-all-read')
        return { success: true, message: data.message }
    } catch (error: any) {
        console.error('Error marking all notifications as read:', error)
        return { 
            success: false, 
            message: error.response?.data?.message || 'Failed to mark all notifications as read' 
        }
    }
}

/**
 * Delete notification
 */
export async function deleteNotification(notificationId: number): Promise<{ success: boolean; message: string }> {
    try {
        const { data } = await clientAxiosInstance.delete(`/notifications/${notificationId}`)
        return { success: true, message: data.message }
    } catch (error: any) {
        console.error('Error deleting notification:', error)
        return { 
            success: false, 
            message: error.response?.data?.message || 'Failed to delete notification' 
        }
    }
}
