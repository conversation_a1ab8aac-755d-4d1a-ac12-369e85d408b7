import { DashContentTable, TableTd, TableTdMain, TableThead, TableTr } from "../DashCrudContent";
import SectionActions from "@/lib/ui/forms/section/actions";
import { SectionResponse } from "@/lib/server/types/section/section";

interface SectionsTableProps {
    sections: SectionResponse;
}

export default function SectionsTable({ sections }: SectionsTableProps) {

    return (
        <>
            <DashContentTable>
                <TableThead list={['Number', 'Year', 'Department', 'Groups', 'Settings']} />
                <tbody>
                    {sections?.data.map((section) => (
                        <TableTr key={section.id}>
                            <TableTdMain value={section.number.toString()} />
                            <TableTd>
                                {section.year.name}
                            </TableTd>
                            <TableTd>
                                {section.year.department.name}
                            </TableTd>
                            <TableTd>
                                {section.groups_count}
                            </TableTd>
                            <TableTd>
                                <SectionActions section={section} />
                            </TableTd>
                        </TableTr>
                    ))}
                </tbody>
            </DashContentTable>
        </>
    )
}

