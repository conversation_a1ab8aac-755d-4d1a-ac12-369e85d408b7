"use client";
import { useState } from "react";
import Button from "@/lib/ui/components/global/Buttons/Button";
import Dialog from "@/lib/ui/components/global/Dialog/Dialog";
import CreateYearForm from "@/lib/ui/forms/year/CreateYearForm";
import { Plus } from "lucide-react";

export default function CreateYearDialog() {
    const [open, setOpen] = useState(false);
    return (
        <>
            <Button mode="filled" icon={<Plus />} onClick={() => setOpen(true)}>
                Create Year
            </Button>
            <Dialog isOpen={open} onClose={() => setOpen(false)} title="Create Year">
                <CreateYearForm onSuccess={() => setOpen(false)} />
            </Dialog>
        </>
    );
}
