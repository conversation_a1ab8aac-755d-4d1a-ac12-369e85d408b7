"use client";

import { Input } from "@/lib/ui/components/global/Inputs/inputs";
import { useForm } from "react-hook-form";
import Button from "@/lib/ui/components/global/Buttons/Button";
import { useRouter, useSearchParams } from "next/navigation";
import { Search } from "lucide-react";

interface SearchFormData {
    search: string;
}

export default function SearchStudents() {
    const router = useRouter();
    const searchParams = useSearchParams();
    const { register, handleSubmit } = useForm<SearchFormData>({
        defaultValues: {
            search: searchParams.get("search") || "",
        },
    });

    const onSubmit = (data: SearchFormData) => {
        const params = new URLSearchParams(searchParams.toString());
        if (data.search) {
            params.set("search", data.search);
        } else {
            params.delete("search");
        }
        params.set("page", "1"); // Reset to first page when searching
        router.push(`/dashboard/students?${params.toString()}`);
    };

    return (
        <form onSubmit={handleSubmit(onSubmit)} className="flex items-center gap-2">
            <Input
                type="text"
                placeholder="Search students..."
                label="search"
                title=""
                register={register}
            />
            <Button type="submit" mode="filled" icon={<Search />}>
                Search
            </Button>
        </form>
    );
} 