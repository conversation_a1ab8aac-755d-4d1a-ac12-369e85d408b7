'use server'

import { Year, CreateYearRequest, YearErrorResponse } from '@/lib/server/types/year/year'
import axiosInstance from '@/lib/server/tools/axios'
import { revalidatePath } from 'next/cache'

interface YearPaginatedResponse {
    data: Year[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
}

export async function getYears(): Promise<YearPaginatedResponse> {
    try {
        const { data } = await axiosInstance.get<YearPaginatedResponse>(`/years`)
        return data
    } catch (error: any) {
        console.error('Error fetching years:', error.response?.data)
        throw error
    }
}

export async function createYear(yearData: CreateYearRequest): Promise<Year | YearErrorResponse> {
    try {
        const { data } = await axiosInstance.post<Year>(
            `/years`,
            yearData
        )
        // Revalidate the years page and related paths
        revalidatePath('/dashboard/years')
        revalidatePath('/dashboard/years', 'page')
        revalidatePath('/dashboard')
        return data
    } catch (error: any) {
        console.error('Error creating year:', error.response?.data)
        if (error.response?.data) {
            return error.response.data as YearErrorResponse
        }
        throw error
    }
}

export async function updateYear(id: number, yearData: Partial<Year>): Promise<Year | YearErrorResponse> {
    try {
        const { data } = await axiosInstance.put<Year>(
            `/years/${id}`,
            yearData
        )
        // Revalidate the years page and related paths
        revalidatePath('/dashboard/years')
        revalidatePath('/dashboard/years', 'page')
        revalidatePath('/dashboard')
        return data
    } catch (error: any) {
        console.error('Error updating year:', error.response?.data)
        if (error.response?.data) {
            return error.response.data as YearErrorResponse
        }
        throw error
    }
}

export async function deleteYear(id: number): Promise<{ success: boolean }> {
    try {
        await axiosInstance.delete(`/years/${id}`)
        // Revalidate the years page and related paths
        revalidatePath('/dashboard/years')
        revalidatePath('/dashboard/years', 'page')
        revalidatePath('/dashboard')
        return { success: true }
    } catch (error) {
        console.error('Error deleting year:', error)
        throw error
    }
}
