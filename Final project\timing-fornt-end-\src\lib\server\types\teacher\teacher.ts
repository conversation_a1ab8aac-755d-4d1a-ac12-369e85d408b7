export interface User {
    id: number;
    email: string;
    email_verified_at: string | null;
    created_at: string;
    updated_at: string;
    key_id: number;
}

export interface Key {
    id: number;
    value: string;
    status: 'used' | 'unused';
    keyable_type: string;
    keyable_id: number;
    used_at: string | null;
    expires_at: string | null;
    created_at: string;
    updated_at: string;
    user?: User;
}

export interface Teacher {
    id: number;
    username: string;
    name: string;
    last: string;
    date_of_birth: string;
    grade?: string;
    research_field?: string;
    created_at: string;
    updated_at: string;
    baladiya_id: number;
    baladiya?: {
        id: number;
        name: string;
        wilaya?: {
            id: number;
            name: string;
        };
    };
    key?: {
        id: number;
        value: string;
        user?: {
            id: number;
            email: string;
        };
    };
}

export interface CreateTeacherRequest {
    username: string;
    name: string;
    last: string;
    date_of_birth: string;
    grade?: string;
    research_field?: string;
    baladiya_id: number;
}

export interface TeacherErrorResponse {
    message: string;
    errors: {
        [key: string]: string[];
    };
}

export interface PaginationLink {
    url: string | null;
    label: string;
    active: boolean;
}

export interface TeacherResponse {
    current_page: number;
    data: Teacher[];
    first_page_url: string;
    from: number;
    last_page: number;
    last_page_url: string;
    links: {
        url: string | null;
        label: string;
        active: boolean;
    }[];
    next_page_url: string | null;
    path: string;
    per_page: number;
    prev_page_url: string | null;
    to: number;
    total: number;
}