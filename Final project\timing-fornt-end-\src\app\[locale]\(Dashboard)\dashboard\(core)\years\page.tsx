import Button from "@/lib/ui/components/global/Buttons/Button";
import {
    DashContent,
    DashContentAction,
    DashContenTitle,
    DashContentStat,
    DashContentStatItem,
    DashContentTable,
    TableTd,
    TableTdMain,
    TableThead,
    TableTr
} from "@/lib/ui/components/local/Dashboard/DashCrudContent";
import { Pencil, Timer, Trash, UserPen } from "lucide-react";
import Link from "next/link";
import CreateYearDialog from "./CreateYearDialog";
import { getYears } from "@/lib/server/actions/year/yearActions";

type Year = {
    id: number;
    name: string;
    year: number;
    department: {
        id: number;
        name: string;
    };
};

export default async function YearsPage() {
    const yearsData = await getYears();
    const years = yearsData.data || [];

    return (
        <DashContent>
            <DashContenTitle>Years</DashContenTitle>
            <DashContentStat>
                <DashContentStatItem title="Total Years" value={years.length.toString()} icon={<UserPen size={80} />} />
            </DashContentStat>
            <DashContentAction>
                <CreateYearDialog />
            </DashContentAction>
            <DashContentTable>
                <TableThead list={['Year Name', 'Department', 'Settings']} />
                <tbody>
                    {years.map((year) => (
                        <TableTr key={year.id}>
                            <TableTdMain value={year.name} />
                            <TableTd>{year.department.name}</TableTd>
                            <TableTd>
                                <div className="flex items-center gap-1">
                                    <Link href={`/dashboard/years/${year.id}`}>
                                        <Pencil className="text-green-700 dark:text-green-400" size={16} />
                                    </Link>
                                    <Link href={`/dashboard/years/${year.id}`}>
                                        <Trash className="text-error dark:text-dark-error" size={16} />
                                    </Link>                                    
                                </div>
                            </TableTd>
                        </TableTr>
                    ))}
                </tbody>
            </DashContentTable>
        </DashContent>
    );
}
