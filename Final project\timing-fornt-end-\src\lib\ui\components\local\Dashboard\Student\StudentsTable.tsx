import { getStudents } from "@/lib/server/actions/student/getStudents";
import { DashContentTable, TableTd, TableTdMain, TableThead, TableTr } from "../DashCrudContent";
import StudentActions from "@/lib/ui/forms/student/actions";
import CreateStudentKey from "@/lib/ui/forms/student/createKey";

interface StudentsTableProps {
    page: string;
    search: string;
}

export default async function StudentsTable({ page, search }: StudentsTableProps) {
    const currentPage = parseInt(page) || 1;
    const students = await getStudents(currentPage, search);

    return (
        <>
            <DashContentTable>
                <TableThead list={['Username', 'Key', 'Name', 'Last', 'Date of Birth', 'Section', 'Group', 'Year - Departement', 'Email', 'Settings']} />
                <tbody>
                    {students?.data.map((student) => (
                        <TableTr key={student.id}>
                            <TableTdMain value={student.username} />
                            <TableTd>
                                {student.key?.value || <CreateStudentKey student={student} />}
                            </TableTd>
                            <TableTd>
                                {student.name}
                            </TableTd>
                            <TableTd>
                                {student.last}
                            </TableTd>
                            <TableTd>
                                {student.date_of_birth}
                            </TableTd>
                            <TableTd>
                                {student.group?.section?.number || 'No Section'}
                            </TableTd>
                            <TableTd>
                                {student.group?.number || 'No Group'}
                            </TableTd>
                            <TableTd>
                                {student.group?.section?.year?.name || 'No Year'} - {student.group?.section?.year?.department?.name || 'No Department'}
                            </TableTd>
                            <TableTd>
                                {student?.key?.user?.email || 'No Email'}
                            </TableTd>
                            <TableTd>
                                <StudentActions student={student} />
                            </TableTd>
                        </TableTr>
                    ))}
                </tbody>
            </DashContentTable>
        </>
    )
}

export interface DepartmentResponse {
    departments: Department[];
}

export interface Department {
    id: number;
    name: string;
    created_at: string;
    updated_at: string;
    years: Year[];
}

export interface Year {
    id: number;
    name: string;
    created_at: string;
    updated_at: string;
    department_id: number;
    sections: Section[];
}

export interface Section {
    id: number;
    number: number;
    created_at: string;
    updated_at: string;
    year_id: number;
    groups: Group[];
}

