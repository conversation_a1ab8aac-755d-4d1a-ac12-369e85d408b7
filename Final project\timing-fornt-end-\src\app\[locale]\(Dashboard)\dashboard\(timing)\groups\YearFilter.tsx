"use client";
import { useRouter, useSearchParams } from "next/navigation";

interface YearFilterProps {
  allYears: { id: number; name: string; departmentName: string }[];
  selectedYear: string;
}

export default function YearFilter({ allYears, selectedYear }: YearFilterProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const params = new URLSearchParams(searchParams.toString());
    if (e.target.value) {
      params.set("year", e.target.value);
    } else {
      params.delete("year");
    }
    params.set("page", "1");
    router.push(`?${params.toString()}`);
  };

  return (
    <form className="flex items-center gap-2">
      <label htmlFor="year" className="font-medium">Year:</label>
      <select
        id="year"
        name="year"
        value={selectedYear}
        className="border rounded px-2 py-1"
        onChange={handleChange}
      >
        <option value="">All Years</option>
        {allYears.map(year => (
          <option key={year.id} value={year.id}>
            {year.name} ({year.departmentName})
          </option>
        ))}
      </select>
    </form>
  );
} 