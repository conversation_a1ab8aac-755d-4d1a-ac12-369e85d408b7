'use server'

import axiosInstance from '@/lib/server/tools/axios'
import { revalidatePath } from 'next/cache'

export interface CreateRequestData {
    teacher_id: number
    section_id?: number
    group_id?: number
    module_id: number
    class_rome_id: number
    day: string
    start_time: string
    end_time: string
    type: 'td' | 'tp' | 'course'
    message?: string
}

export interface RequestResponse {
    id: number
    teacher_id: number
    section_id?: number
    group_id?: number
    module_id: number
    class_rome_id: number
    day: string
    start_time: string
    end_time: string
    type: string
    status: 'pending' | 'approved' | 'rejected'
    message?: string
    admin_response?: string
    reviewed_by?: number
    reviewed_at?: string
    created_at: string
    updated_at: string
    teacher: {
        id: number
        name: string
        last: string
        username: string
    }
    module: {
        id: number
        name: string
    }
    classRome: {
        id: number
        name: string
    }
    section?: {
        id: number
        name: string
    }
    group?: {
        id: number
        number: string
    }
    reviewedBy?: {
        id: number
        name: string
        last: string
    }
}

export interface RequestsListResponse {
    data: RequestResponse[]
    current_page: number
    last_page: number
    per_page: number
    total: number
}

export async function createRequest(requestData: CreateRequestData): Promise<{ success: boolean; message: string; request?: RequestResponse }> {
    try {
        const { data } = await axiosInstance.post('/requests', requestData)
        
        revalidatePath('/dashboard')
        revalidatePath('/dashboard/requests')
        
        return {
            success: true,
            message: 'Request submitted successfully',
            request: data.request
        }
    } catch (error: any) {
        console.error('Error creating request:', error.response?.data)
        return {
            success: false,
            message: error.response?.data?.message || 'Failed to submit request'
        }
    }
}

export async function getRequests(page: number = 1, status?: string): Promise<RequestsListResponse> {
    try {
        let url = `/requests?page=${page}`
        if (status) {
            url += `&status=${status}`
        }
        
        const { data } = await axiosInstance.get<RequestsListResponse>(url)
        return data
    } catch (error: any) {
        console.error('Error fetching requests:', error.response?.data)
        throw error
    }
}

export async function updateRequestStatus(
    requestId: number, 
    status: 'approved' | 'rejected', 
    adminResponse?: string
): Promise<{ success: boolean; message: string }> {
    try {
        await axiosInstance.put(`/requests/${requestId}`, {
            status,
            admin_response: adminResponse
        })
        
        revalidatePath('/dashboard')
        revalidatePath('/dashboard/requests')
        
        return {
            success: true,
            message: `Request ${status} successfully`
        }
    } catch (error: any) {
        console.error('Error updating request:', error.response?.data)
        return {
            success: false,
            message: error.response?.data?.message || 'Failed to update request'
        }
    }
}

export async function getPendingRequestsCount(): Promise<number> {
    try {
        const { data } = await axiosInstance.get('/requests-pending-count')
        return data.count
    } catch (error: any) {
        console.error('Error fetching pending requests count:', error.response?.data)
        return 0
    }
}

export async function getTeacherRequests(page: number = 1): Promise<RequestsListResponse> {
    try {
        const { data } = await axiosInstance.get<RequestsListResponse>(`/teacher-requests?page=${page}`)
        return data
    } catch (error: any) {
        console.error('Error fetching teacher requests:', error.response?.data)
        throw error
    }
}
