"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(Dashboard)/dashboard/(core)/modules/page",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f7ca493359cf\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHByb1xcRGVza3RvcFxcZmluIHByb2ogY29waWUgMlxcRmluYWwgcHJvamVjdFxcdGltaW5nLWZvcm50LWVuZC1cXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImY3Y2E0OTMzNTljZlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/server/actions/module/moduleActions.ts":
/*!********************************************************!*\
  !*** ./src/lib/server/actions/module/moduleActions.ts ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createModule: () => (/* binding */ createModule),\n/* harmony export */   deleteModule: () => (/* binding */ deleteModule),\n/* harmony export */   getModules: () => (/* binding */ getModules),\n/* harmony export */   updateModule: () => (/* binding */ updateModule)\n/* harmony export */ });\n/* harmony import */ var private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! private-next-rsc-action-client-wrapper */ \"(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js\");\n/* harmony import */ var private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_action_entry_do_not_use__ {\"00c8caa8f01d2628d9f12e4d1d7391367e5bcd0a8a\":\"getModules\",\"4095a56f66fe5c6ebd87038dc22d1a1f696c20660f\":\"createModule\",\"40a21a8382b849be46db832415a8a5fef454c24226\":\"deleteModule\",\"60c55de286c4368e49faf01f598a3e915f067f75cd\":\"updateModule\"} */ \nvar getModules = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"00c8caa8f01d2628d9f12e4d1d7391367e5bcd0a8a\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getModules\");\nvar createModule = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"4095a56f66fe5c6ebd87038dc22d1a1f696c20660f\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"createModule\");\nvar updateModule = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"60c55de286c4368e49faf01f598a3e915f067f75cd\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"updateModule\");\nvar deleteModule = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"40a21a8382b849be46db832415a8a5fef454c24226\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"deleteModule\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/server/actions/module/moduleActions.ts\n"));

/***/ })

});