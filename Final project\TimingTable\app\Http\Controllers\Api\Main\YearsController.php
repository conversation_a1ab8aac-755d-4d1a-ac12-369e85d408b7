<?php

namespace App\Http\Controllers\Api\Main;

use App\Http\Controllers\Controller;
use App\Models\Api\Main\Year;
use App\Models\Api\Core\Department;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class YearsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): JsonResponse
    {
        $years = Year::with(['department', 'sections.groups'])
            ->paginate(10);
        
        return response()->json($years);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'year' => 'required|integer|min:1|max:10',
            'department_id' => 'required|exists:departments,id',
        ]);

        $year = Year::create([
            'name' => $request->name,
            'year' => $request->year,
            'department_id' => $request->department_id,
        ]);

        return response()->json($year->load(['department', 'sections.groups']), 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Year $year): JsonResponse
    {
        return response()->json($year->load(['department', 'sections.groups']));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Year $year): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'year' => 'required|integer|min:1|max:10',
            'department_id' => 'required|exists:departments,id',
        ]);

        $year->update([
            'name' => $request->name,
            'year' => $request->year,
            'department_id' => $request->department_id,
        ]);

        return response()->json($year->load(['department', 'sections.groups']));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Year $year): JsonResponse
    {
        $year->delete();
        
        return response()->json(['message' => 'Year deleted successfully']);
    }
}
