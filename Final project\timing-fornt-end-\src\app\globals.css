@import "tailwindcss";

@layer base {
  :root {
    --font-cairo: 'Cairo', sans-serif;
  }

  html {
    font-family: var(--font-cairo);
  }
}

@theme {
  /* Font Family */
  --font-family: var(--font-cairo), system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

  --color-primary: rgb(39 74 121);
  --color-surface-tint: rgb(62 95 144);
  --color-on-primary: rgb(255 255 255);
  --color-primary-container: rgb(78 110 160);
  --color-on-primary-container: rgb(255 255 255);
  --color-secondary: rgb(83 95 116);
  --color-on-secondary: rgb(255 255 255);
  --color-secondary-container: rgb(220 231 255);
  --color-on-secondary-container: rgb(63 75 95);
  --color-tertiary: rgb(99 57 104);
  --color-on-tertiary: rgb(255 255 255);
  --color-tertiary-container: rgb(139 92 142);
  --color-on-tertiary-container: rgb(255 255 255);
  --color-error: rgb(186 26 26);
  --color-on-error: rgb(255 255 255);
  --color-error-container: rgb(255 218 214);
  --color-on-error-container: rgb(65 0 2);
  --color-background: rgb(250 249 253);
  --color-on-background: rgb(26 28 31);
  --color-surface: rgb(250 249 253);
  --color-on-surface: rgb(26 28 31);
  --color-surface-variant: rgb(223 226 236);
  --color-on-surface-variant: rgb(67 71 79);
  --color-outline: rgb(115 119 128);
  --color-outline-variant: rgb(195 198 208);
  --color-shadow: rgb(0 0 0);
  --color-scrim: rgb(0 0 0);
  --color-inverse-surface: rgb(47 48 52);
  --color-inverse-on-surface: rgb(241 240 245);
  --color-inverse-primary: rgb(167 200 255);
  --color-primary-fixed: rgb(213 227 255);
  --color-on-primary-fixed: rgb(0 27 60);
  --color-primary-fixed-dim: rgb(167 200 255);
  --color-on-primary-fixed-variant: rgb(36 71 119);
  --color-secondary-fixed: rgb(215 227 252);
  --color-on-secondary-fixed: rgb(16 28 46);
  --color-secondary-fixed-dim: rgb(187 199 223);
  --color-on-secondary-fixed-variant: rgb(60 71 91);
  --color-tertiary-fixed: rgb(255 214 254);
  --color-on-tertiary-fixed: rgb(49 9 56);
  --color-tertiary-fixed-dim: rgb(235 181 237);
  --color-on-tertiary-fixed-variant: rgb(97 55 102);
  --color-surface-dim: rgb(218 217 222);
  --color-surface-bright: rgb(250 249 253);
  --color-surface-container-lowest: rgb(255 255 255);
  --color-surface-container-low: rgb(244 243 248);
  --color-surface-container: rgb(238 237 242);
  --color-surface-container-high: rgb(232 232 236);
  --color-surface-container-highest: rgb(227 226 230);
  --color-dark-primary: rgb(167 200 255);
  --color-dark-surface-tint: rgb(167 200 255);
  --color-dark-on-primary: rgb(4 48 95);
  --color-dark-primary-container: rgb(52 85 133);
  --color-dark-on-primary-container: rgb(246 247 255);
  --color-dark-secondary: rgb(187 199 223);
  --color-dark-on-secondary: rgb(37 49 68);
  --color-dark-secondary-container: rgb(52 63 83);
  --color-dark-on-secondary-container: rgb(200 212 236);
  --color-dark-tertiary: rgb(235 181 237);
  --color-dark-on-tertiary: rgb(73 32 78);
  --color-dark-tertiary-container: rgb(113 69 117);
  --color-dark-on-tertiary-container: rgb(255 246 250);
  --color-dark-error: rgb(255 180 171);
  --color-dark-on-error: rgb(105 0 5);
  --color-dark-error-container: rgb(147 0 10);
  --color-dark-on-error-container: rgb(255 218 214);
  --color-dark-background: rgb(18 19 23);
  --color-dark-on-background: rgb(227 226 230);
  --color-dark-surface: rgb(18 19 23);
  --color-dark-on-surface: rgb(227 226 230);
  --color-dark-surface-variant: rgb(67 71 79);
  --color-dark-on-surface-variant: rgb(195 198 208);
  --color-dark-outline: rgb(141 145 154);
  --color-dark-outline-variant: rgb(67 71 79);
  --color-dark-shadow: rgb(0 0 0);
  --color-dark-scrim: rgb(0 0 0);
  --color-dark-inverse-surface: rgb(227 226 230);
  --color-dark-inverse-on-surface: rgb(47 48 52);
  --color-dark-inverse-primary: rgb(62 95 144);
  --color-dark-primary-fixed: rgb(213 227 255);
  --color-dark-on-primary-fixed: rgb(0 27 60);
  --color-dark-primary-fixed-dim: rgb(167 200 255);
  --color-dark-on-primary-fixed-variant: rgb(36 71 119);
  --color-dark-secondary-fixed: rgb(215 227 252);
  --color-dark-on-secondary-fixed: rgb(16 28 46);
  --color-dark-secondary-fixed-dim: rgb(187 199 223);
  --color-dark-on-secondary-fixed-variant: rgb(60 71 91);
  --color-dark-tertiary-fixed: rgb(255 214 254);
  --color-dark-on-tertiary-fixed: rgb(49 9 56);
  --color-dark-tertiary-fixed-dim: rgb(235 181 237);
  --color-dark-on-tertiary-fixed-variant: rgb(97 55 102);
  --color-dark-surface-dim: rgb(18 19 23);
  --color-dark-surface-bright: rgb(56 57 61);
  --color-dark-surface-container-lowest: rgb(13 14 17);
  --color-dark-surface-container-low: rgb(26 28 31);
  --color-dark-surface-container: rgb(30 32 35);
  --color-dark-surface-container-high: rgb(41 42 45);
  --color-dark-surface-container-highest: rgb(51 53 56);
  /* Display */
  --text-display-large: 4.1rem;
  --text-display-large--line-height: 5.2rem;
  --text-display-large--letter-spacing: -0.02em;
  --text-display-large--font-weight: 400;

  --text-display-medium: 3.2rem;
  --text-display-medium--line-height: 4rem;
  --text-display-medium--letter-spacing: 0em;
  --text-display-medium--font-weight: 400;

  --text-display-small: 2.8rem;
  --text-display-small--line-height: 3.6rem;
  --text-display-small--letter-spacing: 0em;
  --text-display-small--font-weight: 400;

  /* Headline */
  --text-headline-large: 2.4rem;
  --text-headline-large--line-height: 3.2rem;
  --text-headline-large--letter-spacing: 0em;
  --text-headline-large--font-weight: 400;

  --text-headline-medium: 2rem;
  --text-headline-medium--line-height: 2.8rem;
  --text-headline-medium--letter-spacing: 0em;
  --text-headline-medium--font-weight: 400;

  --text-headline-small: 1.75rem;
  --text-headline-small--line-height: 2.4rem;
  --text-headline-small--letter-spacing: 0em;
  --text-headline-small--font-weight: 400;

  /* Title */
  --text-title-large: 1.4rem;
  --text-title-large--line-height: 2rem;
  --text-title-large--letter-spacing: 0em;
  --text-title-large--font-weight: 400;

  --text-title-medium: 1rem;
  --text-title-medium--line-height: 1.6rem;
  --text-title-medium--letter-spacing: 0.009rem;
  --text-title-medium--font-weight: 500;

  --text-title-small: 0.875rem;
  --text-title-small--line-height: 1.4rem;
  --text-title-small--letter-spacing: 0.006rem;
  --text-title-small--font-weight: 500;

  /* Body */
  --text-body-large: 1rem;
  --text-body-large--line-height: 1.6rem;
  --text-body-large--letter-spacing: 0.031rem;
  --text-body-large--font-weight: 400;

  --text-body-medium: 0.875rem;
  --text-body-medium--line-height: 1.4rem;
  --text-body-medium--letter-spacing: 0.022rem;
  --text-body-medium--font-weight: 400;

  --text-body-small: 0.75rem;
  --text-body-small--line-height: 1.2rem;
  --text-body-small--letter-spacing: 0.018rem;
  --text-body-small--font-weight: 400;

  /* Label */
  --text-label-large: 0.875rem;
  --text-label-large--line-height: 1.2rem;
  --text-label-large--letter-spacing: 0.012rem;
  --text-label-large--font-weight: 500;

  --text-label-medium: 0.75rem;
  --text-label-medium--line-height: 1rem;
  --text-label-medium--letter-spacing: 0.016rem;
  --text-label-medium--font-weight: 500;

  --text-label-small: 0.688rem;
  --text-label-small--line-height: 0.8rem;
  --text-label-small--letter-spacing: 0.012rem;
  --text-label-small--font-weight: 500;
}

@custom-variant dark (&:where(.dark, .dark *));
